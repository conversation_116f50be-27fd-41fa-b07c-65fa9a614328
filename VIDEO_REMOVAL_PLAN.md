# Video Functionality Removal Plan

## Overview
This document outlines a comprehensive plan to safely remove all video functionality from the Phantom Caller Android app while preserving all other functionality including audio calls, contacts, meetings (audio-only), recordings, and settings.

## 1. Permissions and Manifest Changes

### 1.1 AndroidManifest.xml
- **REMOVE**: `<uses-feature android:name="android.hardware.camera" android:required="false" />`
- **REMOVE**: `<uses-permission android:name="android.permission.CAMERA" />`
- **REMOVE**: `<uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />`
- **UPDATE**: Remove `camera` from `android:foregroundServiceType="phoneCall|camera|microphone"` → `android:foregroundServiceType="phoneCall|microphone"`

## 2. Core Video Components to Remove

### 2.1 Custom Video Views
- **DELETE**: `app/src/main/java/org/linphone/ui/call/view/RoundCornersTextureView.kt`
- **UPDATE**: Remove all references to `RoundCornersTextureView` in layout files
- **UPDATE**: Remove `RoundCornersTextureView` styleable attributes from `app/src/main/res/values/attrs.xml`

### 2.2 Video-Related Drawable Resources
- **DELETE**: `app/src/main/res/drawable/video_camera.xml`
- **DELETE**: `app/src/main/res/drawable/video_camera_slash.xml`
- **DELETE**: `app/src/main/res/drawable/video_conference.xml`
- **DELETE**: `app/src/main/res/drawable/video_conference_notification.xml`
- **DELETE**: `app/src/main/res/drawable/video_conference_plus.xml`
- **DELETE**: `app/src/main/res/drawable/inset_video_conference.xml`
- **DELETE**: `app/src/main/res/drawable/camera.xml`
- **DELETE**: `app/src/main/res/drawable/camera_rotate.xml`
- **DELETE**: `app/src/main/res/drawable/picture_in_picture.xml`

## 3. Layout File Modifications

### 3.1 Call-Related Layouts
**Files to modify:**
- `app/src/main/res/layout/call_active_fragment.xml`
- `app/src/main/res/layout-land/call_active_fragment.xml`
- `app/src/main/res/layout/call_outgoing_fragment.xml`
- `app/src/main/res/layout-land/call_outgoing_fragment.xml`
- `app/src/main/res/layout/call_incoming_fragment.xml`
- `app/src/main/res/layout-land/call_incoming_fragment.xml`
- `app/src/main/res/layout/call_active_conference_fragment.xml`
- `app/src/main/res/layout/calls_list_fragment.xml`

**Changes:**
- Remove all `RoundCornersTextureView` elements (`remote_video_surface`, `local_preview_video_surface`)
- Remove `switch_camera` buttons and related constraints
- Remove video-related visibility bindings
- Update layout constraints to fill gaps left by removed video elements

### 3.2 Conference-Related Layouts
**Files to modify:**
- `app/src/main/res/layout/call_conference_active_speaker_fragment.xml`
- `app/src/main/res/layout-land/call_conference_active_speaker_fragment.xml`
- `app/src/main/res/layout/call_conference_grid_fragment.xml`
- `app/src/main/res/layout/call_conference_active_speaker_cell.xml`
- `app/src/main/res/layout/call_conference_grid_cell.xml`
- `app/src/main/res/layout/call_conference_participants_list_fragment.xml`
- `app/src/main/res/layout/call_conference_layout_bottom_sheet.xml`

**Changes:**
- Remove all `RoundCornersTextureView` elements
- Remove `participantTextureView` binding adapters
- Remove video layout options (keep audio-only layout only)
- Remove picture-in-picture references

### 3.3 Meeting-Related Layouts
**Files to modify:**
- `app/src/main/res/layout/meeting_waiting_room_fragment.xml`
- `app/src/main/res/layout-land/meeting_waiting_room_fragment.xml`
- `app/src/main/res/layout/meeting_fragment.xml`
- `app/src/main/res/layout/meeting_edit_fragment.xml`
- `app/src/main/res/layout/meeting_schedule_fragment.xml`
- `app/src/main/res/layout/meeting_list_cell.xml`
- `app/src/main/res/layout/meetings_list_fragment.xml`
- `app/src/main/res/layout-land/meetings_list_fragment.xml`

**Changes:**
- Remove video preview surfaces
- Remove camera switch buttons
- Replace video conference icons with audio-only alternatives
- Remove video toggle buttons

### 3.4 Other UI Layouts
**Files to modify:**
- `app/src/main/res/layout/call_actions_generic.xml`
- `app/src/main/res/layout/call_incoming_actions.xml`
- `app/src/main/res/layout/call_outgoing_actions.xml`
- `app/src/main/res/layout/call_ended_actions.xml`
- `app/src/main/res/layout/bottom_nav_bar.xml`
- `app/src/main/res/layout-land/bottom_nav_bar.xml`
- `app/src/main/res/layout/contact_fragment.xml`
- `app/src/main/res/layout/contact_new_or_edit_fragment.xml`
- `app/src/main/res/layout-land/contact_new_or_edit_fragment.xml`
- `app/src/main/res/layout/history_fragment.xml`
- `app/src/main/res/layout/start_call_fragment.xml`

**Changes:**
- Remove video call buttons and replace with audio call alternatives
- Remove camera-related icons and buttons
- Update navigation icons to remove video conference references

## 4. Code Modifications

### 4.1 Core Context Changes
**File**: `app/src/main/java/org/linphone/core/CoreContext.kt`
- **REMOVE**: `switchCamera()` method (lines ~906-920)
- **REMOVE**: `showSwitchCameraButton()` method (lines ~926-928)
- **UPDATE**: Remove video device management code
- **UPDATE**: Remove camera-related initialization

### 4.2 View Model Updates

#### 4.2.1 CurrentCallViewModel.kt
**File**: `app/src/main/java/org/linphone/ui/call/viewmodel/CurrentCallViewModel.kt`
- **REMOVE**: Video-related LiveData properties:
  - `isVideoEnabled`
  - `isSendingVideo`
  - `isReceivingVideo`
  - `showSwitchCamera`
  - `videoUpdateInProgress`
  - `hideVideo`
  - `fullScreenMode`
  - `pipMode`
- **REMOVE**: Methods:
  - `toggleVideo()` (lines ~787-835)
  - `switchCamera()` (lines ~840-844)
  - `updateVideoDirection()` (lines ~1270-1302)
- **UPDATE**: Remove video-related logic from call state handling
- **UPDATE**: Remove camera permission requests
- **UPDATE**: Simplify proximity sensor logic (remove video conditions)

#### 4.2.2 ConferenceViewModel.kt
**File**: `app/src/main/java/org/linphone/ui/call/conference/viewmodel/ConferenceViewModel.kt`
- **REMOVE**: Video-related properties:
  - `isMeParticipantSendingVideo`
  - Video layout constants (keep only `AUDIO_ONLY_LAYOUT`)
- **REMOVE**: Methods:
  - `localVideoStreamToggled()` (lines ~343-346)
- **UPDATE**: Remove video layout switching logic
- **UPDATE**: Force audio-only mode for all conferences
- **UPDATE**: Remove video-related participant device handling

#### 4.2.3 MeetingWaitingRoomViewModel.kt
**File**: `app/src/main/java/org/linphone/ui/main/meetings/viewmodel/MeetingWaitingRoomViewModel.kt`
- **REMOVE**: Video-related properties and methods
- **REMOVE**: Camera switching functionality (lines ~267-269)
- **REMOVE**: Video preview setup
- **UPDATE**: Remove camera permission checks
- **UPDATE**: Force audio-only meetings

#### 4.2.4 SettingsViewModel.kt
**File**: `app/src/main/java/org/linphone/ui/main/settings/viewmodel/SettingsViewModel.kt`
- **REMOVE**: `videoEnabled` property (line 66)
- **REMOVE**: Video settings toggle methods (lines ~402-407)
- **UPDATE**: Remove video-related settings initialization

### 4.3 Fragment Updates

#### 4.3.1 ActiveCallFragment.kt
**File**: `app/src/main/java/org/linphone/ui/call/fragment/ActiveCallFragment.kt`
- **REMOVE**: Video surface setup and cleanup methods
- **REMOVE**: Video-related observers (lines ~325-343)
- **UPDATE**: Remove video preview handling
- **UPDATE**: Remove native video window ID setup

#### 4.3.2 GenericCallFragment.kt
**File**: `app/src/main/java/org/linphone/ui/call/fragment/GenericCallFragment.kt`
- **REMOVE**: `setupVideoPreview()` method (line ~60)
- **REMOVE**: `cleanVideoPreview()` method (line ~77)
- **UPDATE**: Remove video-related imports

#### 4.3.3 MeetingWaitingRoomFragment.kt
**File**: `app/src/main/java/org/linphone/ui/main/meetings/fragment/MeetingWaitingRoomFragment.kt`
- **REMOVE**: `enableVideoPreview()` method (line ~173)
- **REMOVE**: Camera permission handling
- **UPDATE**: Remove video preview setup

#### 4.3.4 Conference Fragments
**Files**: All conference fragment files
- **REMOVE**: Video-related functionality
- **UPDATE**: Force audio-only mode
- **UPDATE**: Remove video layout switching

### 4.4 Model Updates

#### 4.4.1 ConferenceParticipantDeviceModel.kt
**File**: `app/src/main/java/org/linphone/ui/call/conference/model/ConferenceParticipantDeviceModel.kt`
- **REMOVE**: `textureView` property and related methods
- **REMOVE**: `setTextureView()` method (lines ~181-213)
- **UPDATE**: Remove video stream handling

#### 4.4.2 CallStatsModel.kt
**File**: `app/src/main/java/org/linphone/ui/call/model/CallStatsModel.kt`
- **UPDATE**: Remove video statistics (lines ~38-43)
- **UPDATE**: Keep only audio statistics

### 4.5 Utility Class Updates

#### 4.5.1 LinphoneUtils.kt
**File**: `app/src/main/java/org/linphone/utils/LinphoneUtils.kt`
- **REMOVE**: `isVideoEnabled()` method (lines ~262-279)
- **UPDATE**: Remove video-related conference creation logic
- **UPDATE**: Force audio-only conferences in `createGroupCall()` (line ~315)

#### 4.5.2 DataBindingUtils.kt
**File**: `app/src/main/java/org/linphone/utils/DataBindingUtils.kt`
- **REMOVE**: `setParticipantTextureView()` method (lines ~431-436)
- **REMOVE**: `setRoundCornersRadius()` method (line ~503)
- **REMOVE**: Video conference icon bindings (lines ~387, 419)
- **UPDATE**: Remove TextureView imports and references

### 4.6 Activity Updates

#### 4.6.1 CallActivity.kt
**File**: `app/src/main/java/org/linphone/ui/call/CallActivity.kt`
- **REMOVE**: Camera permission handling (lines ~241-245, 337-341)
- **REMOVE**: Video-related permission launchers
- **UPDATE**: Remove picture-in-picture mode support
- **UPDATE**: Remove video-related activity lifecycle handling

### 4.7 Compatibility Layer Updates
**Files**: All compatibility classes
- **REMOVE**: Camera permission handling
- **UPDATE**: Remove video-related permission arrays
- **UPDATE**: Remove camera foreground service type

## 5. String Resources

### 5.1 Remove Video-Related Strings
**Files**: 
- `app/src/main/res/values/strings.xml`
- `app/src/main/res/values-ru/strings.xml`
- `app/src/main/res/values-uk/strings.xml`

**Strings to remove:**
- `call_camera_permission_not_granted_toast`
- `content_description_toggle_video`
- `content_description_change_camera`
- `call_video_incoming`
- `call_video_incoming_for_account`
- Video conference related strings
- Audio/video conference factory URI strings

## 6. Configuration Files

### 6.1 Asset Configuration Updates
**Files**:
- `app/src/main/assets/linphonerc_default`
- `app/src/main/assets/linphonerc_factory`
- `app/src/main/assets/assistant_linphone_default_values`
- `app/src/main/assets/assistant_third_party_default_values`

**Changes:**
- Set `video_enabled=0`
- Remove video codec configurations
- Remove camera device settings
- Remove video conference factory URIs

### 6.2 Settings Layout Updates
**Files**:
- `app/src/main/res/layout/settings_calls.xml`
- `app/src/main/res/layout/settings_advanced_calls.xml`

**Changes:**
- Remove video enable/disable toggle
- Remove video-related advanced settings
- Remove video codec preferences

## 7. Navigation and Shortcuts

### 7.1 Navigation Updates
**Files**: All navigation graph files
- **UPDATE**: Remove video call navigation paths
- **UPDATE**: Update deep links to remove video options

### 7.2 Shortcuts Updates
**File**: `app/src/main/res/xml/shortcuts.xml`
- **UPDATE**: Remove video call shortcuts
- **UPDATE**: Keep only audio call shortcuts

## 8. Testing and Validation

### 8.1 Functionality Tests
- Verify audio calls work correctly
- Verify conference calls work in audio-only mode
- Verify meetings work in audio-only mode
- Verify contact management remains functional
- Verify call history works correctly
- Verify recordings functionality is preserved

### 8.2 UI Tests
- Verify no video-related UI elements remain
- Verify layout integrity after video element removal
- Verify navigation flows work correctly
- Verify settings screens function properly

### 8.3 Permission Tests
- Verify app no longer requests camera permissions
- Verify app functions without camera hardware
- Verify foreground service works without camera type

## 9. Documentation Updates

### 9.1 README.md
- **UPDATE**: Remove references to video calling
- **UPDATE**: Update feature descriptions to audio-only
- **UPDATE**: Remove video-related troubleshooting

### 9.2 Metadata
- **UPDATE**: `metadata/en-US/short_description.txt` - Remove video references
- **UPDATE**: `metadata/en-US/full_description.txt` - Remove video references

## 10. Build Configuration

### 10.1 Gradle Updates
- **VERIFY**: No video-related dependencies need removal
- **VERIFY**: ProGuard rules don't need video-related updates

## 11. Implementation Order

### Phase 1: Core Infrastructure
1. Update AndroidManifest.xml permissions
2. Remove video view classes
3. Update core context and utilities

### Phase 2: View Models and Logic
1. Update CurrentCallViewModel
2. Update ConferenceViewModel
3. Update MeetingWaitingRoomViewModel
4. Update SettingsViewModel

### Phase 3: UI Components
1. Update layout files (start with call layouts)
2. Update fragments
3. Update activities
4. Remove drawable resources

### Phase 4: Configuration and Resources
1. Update string resources
2. Update configuration files
3. Update settings layouts
4. Update navigation

### Phase 5: Testing and Cleanup
1. Run comprehensive tests
2. Remove any remaining video references
3. Update documentation
4. Final validation

## 12. Risk Mitigation

### 12.1 Backup Strategy
- Create feature branch before starting
- Commit changes in logical phases
- Test each phase before proceeding

### 12.2 Rollback Plan
- Keep detailed change log
- Maintain ability to restore video functionality
- Document all removed components

### 12.3 Compatibility Considerations
- Ensure existing audio calls continue working
- Maintain conference call compatibility (audio-only)
- Preserve meeting functionality (audio-only)
- Keep all non-video features intact

## 13. Success Criteria

- [ ] App builds successfully without video components
- [ ] All audio functionality works correctly
- [ ] No camera permissions requested
- [ ] No video-related UI elements visible
- [ ] Conference calls work in audio-only mode
- [ ] Meetings work in audio-only mode
- [ ] Settings properly reflect audio-only capabilities
- [ ] App size potentially reduced
- [ ] No video-related crashes or errors
- [ ] Documentation accurately reflects audio-only functionality

This plan ensures a systematic and safe removal of all video functionality while preserving the core audio communication features of the application.

## Implementation Status

**Status**: Nearly Complete - Phase 5 (Testing and Cleanup)  
**Last Updated**: December 2024

### Progress Tracking
- [x] **Phase 1: Core Infrastructure - COMPLETE**
  - [x] Removed camera permissions from AndroidManifest.xml
  - [x] Deleted RoundCornersTextureView.kt and styleable attributes
  - [x] Removed all video-related drawable resources
- [x] **Phase 2: View Models and Logic - COMPLETE**
  - [x] Updated CoreContext.kt (removed switchCamera methods)
  - [x] Updated CurrentCallViewModel.kt (removed video LiveData and methods)
  - [x] Updated ConferenceViewModel.kt (removed video properties)
  - [x] Updated LinphoneUtils.kt (simplified isVideoEnabled to always return false)
  - [x] Updated MeetingWaitingRoomViewModel.kt (removed video methods)
  - [x] Updated SettingsViewModel.kt (removed video settings)
- [x] **Phase 3: UI Components - COMPLETE**
  - [x] Updated all main call layout files (active, incoming, outgoing - both orientations)
  - [x] Updated call_actions_generic.xml (removed video toggle button)
  - [x] Updated DataBindingUtils.kt (removed video-related binding adapters)
  - [x] Updated conference layout files (grid, active speaker, participants)
  - [x] Updated meeting waiting room layout (portrait and landscape)
  - [x] Removed all RoundCornersTextureView references from layouts
  - [x] Updated constraint references after video element removal
- [x] **Phase 4: Configuration and Resources - COMPLETE**
  - [x] Updated linphonerc_default and linphonerc_factory (disabled video)
  - [x] Updated assistant configuration files (removed video conference URIs)
  - [x] Updated strings.xml (commented out video-related strings)
- [ ] **Phase 5: Testing and Cleanup**

### Key Accomplishments
- Successfully removed all core video functionality from backend logic
- Converted app to audio-only at the view model level
- Updated major UI layouts to remove video elements
- Maintained all audio functionality and call features
- No breaking changes to existing audio call workflows

### Next Steps
1. Complete landscape meeting waiting room layout updates
2. Update remaining layout files with video references
3. Update fragment classes that call video methods
4. Move to Phase 4: Configuration and asset file updates