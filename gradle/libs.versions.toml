[versions]
agp = "8.9.0"
kotlin = "2.0.21"
gmsGoogleServices = "4.4.2"
firebaseBomVersion = "33.10.0"
ktlint = "12.1.2"

annotations = "1.9.1"
activity = "1.10.1"
appcompat = "1.7.0"
constraintLayout = "2.2.1"
coreKtx = "1.15.0"
splashscreen = "1.2.0-beta01"
telecom = "1.0.0"
media = "1.7.0"
recyclerview = "1.4.0"
slidingpanelayout = "1.2.0"
window = "1.3.0"
gridlayout = "1.0.0"
securityCryptoKtx = "1.1.0-alpha06"
navigation = "2.8.8"
# Chat functionality removed - emoji picker no longer needed
# emoji2 = "1.5.0"
car = "1.7.0-rc01"
# Chat functionality removed - flexbox layout no longer needed
# flexbox = "3.0.0"
material = "1.12.0"
protobuf = "3.25.5"
coil = "3.1.0"
# Chat functionality removed - dots indicator no longer needed
# dotsIndicator = "5.1.0"
# Chat functionality removed - photo viewer no longer needed
# photoview = "2.3.0"
openidAppauth = "0.11.1"
linphone = "5.4.+"

[libraries]
androidx-annotations = { group = "androidx.annotation", name = "annotation", version.ref = "annotations" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-constraint-layout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintLayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }
androidx-telecom = { group = "androidx.core", name = "core-telecom", version.ref = "telecom" }
androidx-media = { group = "androidx.media", name = "media", version.ref = "media" }
androidx-recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
androidx-slidingpanelayout = { group = "androidx.slidingpanelayout", name = "slidingpanelayout", version.ref = "slidingpanelayout" }
androidx-window = { group = "androidx.window", name = "window", version.ref = "window" }
androidx-gridlayout = { group = "androidx.gridlayout", name = "gridlayout", version.ref = "gridlayout" }
androidx-security-crypto-ktx = { group = "androidx.security", name = "security-crypto-ktx", version.ref = "securityCryptoKtx" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigation" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigation" }
# Chat functionality removed - emoji picker library no longer needed
# androidx-emoji2 = { group = "androidx.emoji2", name = "emoji2-emojipicker", version.ref = "emoji2" }
androidx-car = { group = "androidx.car.app", name = "app", version.ref = "car" }

google-firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBomVersion" }
google-firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
# Chat functionality removed - flexbox layout library no longer needed
# google-flexbox = { group = "com.google.android.flexbox", name = "flexbox", version.ref = "flexbox" }
google-material = { group = "com.google.android.material", name = "material", version.ref = "material" }
google-protobuf = { group = "com.google.protobuf", name = "protobuf-javalite", version.ref = "protobuf" }

coil = { group = "io.coil-kt.coil3", name = "coil", version.ref = "coil" }
coil-gif = { group = "io.coil-kt.coil3", name = "coil-gif", version.ref = "coil" }
coil-svg = { group = "io.coil-kt.coil3", name = "coil-svg", version.ref = "coil" }
coil-video = { group = "io.coil-kt.coil3", name = "coil-video", version.ref = "coil" }
# Chat functionality removed - dots indicator library no longer needed
# dots-indicator = { group = "com.tbuonomo", name = "dotsindicator", version.ref = "dotsIndicator" }
# Chat functionality removed - photo viewer library no longer needed
# photoview = { group = "com.github.chrisbanes", name = "PhotoView", version.ref = "photoview" }
openid-appauth = { group = "net.openid", name = "appauth", version.ref = "openidAppauth" }

linphone = { group = "org.linphone", name = "linphone-sdk-android", version.ref = "linphone" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
navigation = { id = "androidx.navigation.safeargs.kotlin", version.ref = "navigation" }
googleGmsServices = { id = "com.google.gms.google-services", version.ref = "gmsGoogleServices" }
ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "ktlint" }