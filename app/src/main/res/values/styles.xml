<?xml version='1.0' encoding='UTF-8'?>
<resources>
    <style name="default_text_style_300">
        <item name="android:fontFamily">@font/noto_sans_300</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="default_text_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="default_text_style_500">
        <item name="android:fontFamily">@font/noto_sans_500</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="default_text_style_600">
        <item name="android:fontFamily">@font/noto_sans_600</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="default_text_style_700">
        <item name="android:fontFamily">@font/noto_sans_700</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="default_text_style_800">
        <item name="android:fontFamily">@font/noto_sans_800</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="main_page_title_style">
        <item name="android:fontFamily">@font/noto_sans_800</item>
        <item name="android:textColor">?attr/color_main1_500</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    
    <style name="call_header_style">
        <item name="android:fontFamily">@font/noto_sans_800</item>
        <item name="android:textColor">@color/bc_white</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="section_header_style">
        <item name="android:fontFamily">@font/noto_sans_800</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="header_style">
        <item name="android:fontFamily">@font/noto_sans_700</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textSize">13sp</item>
    </style>
    <style name="settings_title_style">
        <item name="android:fontFamily">@font/noto_sans_700</item>
        <item name="android:textColor">?attr/color_main2_500</item>
        <item name="android:textSize">13sp</item>
    </style>
    <style name="settings_subtitle_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textColor">?attr/color_main2_500</item>
        <item name="android:textSize">14sp</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="primary_button_label_style">
        <item name="android:fontFamily">@font/noto_sans_600</item>
        <item name="android:textColor">@color/primary_button_label_color</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/primary_button_background</item>
        <item name="android:paddingTop">@dimen/primary_secondary_buttons_label_padding</item>
        <item name="android:paddingBottom">@dimen/primary_secondary_buttons_label_padding</item>
    </style>
    <style name="secondary_button_label_style">
        <item name="android:fontFamily">@font/noto_sans_600</item>
        <item name="android:textColor">@color/secondary_button_label_color</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/secondary_button_background</item>
        <item name="android:paddingTop">@dimen/primary_secondary_buttons_label_padding</item>
        <item name="android:paddingBottom">@dimen/primary_secondary_buttons_label_padding</item>
    </style>
    <style name="tertiary_button_label_style">
        <item name="android:fontFamily">@font/noto_sans_600</item>
        <item name="android:textColor">@color/tertiary_button_label_color</item>
        <item name="android:textSize">13sp</item>
    </style>
    <style name="bottom_nav_bar_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="context_menu_action_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?attr/color_main2_500</item>
        <item name="android:gravity">start</item>
        <item name="android:padding">20dp</item>
        <item name="android:drawableTint">?attr/color_main2_600</item>
        <item name="android:drawablePadding">8dp</item>
    </style>
    <style name="context_menu_danger_action_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?attr/color_danger_500</item>
        <item name="android:gravity">start</item>
        <item name="android:padding">20dp</item>
        <item name="android:drawableTint">?attr/color_danger_500</item>
        <item name="android:drawablePadding">8dp</item>
    </style>
    <style name="popup_menu_action_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?attr/color_main2_500</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingStart">15dp</item>
        <item name="android:paddingEnd">15dp</item>
        <item name="android:paddingTop">10dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:drawableTint">?attr/color_main2_500</item>
        <item name="android:drawablePadding">8dp</item>
    </style>
    <style name="popup_menu_danger_action_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?attr/color_danger_500</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingStart">15dp</item>
        <item name="android:paddingEnd">15dp</item>
        <item name="android:paddingTop">10dp</item>
        <item name="android:paddingBottom">10dp</item>
        <item name="android:drawableTint">?attr/color_danger_500</item>
        <item name="android:drawablePadding">8dp</item>
    </style>

    <style name="message_reaction_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textSize">15sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:paddingBottom">5dp</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_marginTop">-10dp</item>
        <item name="android:layout_marginBottom">5dp</item>
    </style>
    <style name="in_call_extra_action_label_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/in_call_label_color</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="material_switch_style">
        <item name="thumbTint">?attr/color_main2_000</item>
        <item name="trackTint">@color/switch_track_color</item>
        <item name="trackDecorationTint">@color/transparent_color</item>
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textColor">?attr/color_text</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="rounded">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="avatar_imageview">
        <item name="android:background">@drawable/shape_circle_contact_avatar_background</item>
        <item name="android:adjustViewBounds">true</item>
        <item name="android:scaleType">centerCrop</item>
    </style>
    <style name="unread_count_text_style">
        <item name="android:fontFamily">@font/noto_sans</item>
        <item name="android:textColor">?attr/color_on_main</item>
        <item name="android:textColorHint">?attr/color_hint_text</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_red_round</item>
        <item name="android:textSize">13sp</item>
        <item name="android:paddingBottom">2dp</item>
        <item name="android:autoSizeTextType">uniform</item>
        <item name="android:autoSizeMinTextSize">8sp</item>
        <item name="android:autoSizeStepGranularity">1sp</item>
    </style>
</resources>
