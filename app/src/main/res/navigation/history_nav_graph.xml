<?xml version="1.0" encoding="utf-8"?>
<!--suppress ALL -->
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/history_nav_graph"
    app:startDestination="@id/emptyFragment">

    <fragment
        android:id="@+id/emptyFragment"
        android:name="org.linphone.ui.main.fragment.EmptyFragment"
        android:label="EmptyFragment"
        tools:layout="@layout/empty_fragment"/>

    <fragment
        android:id="@+id/historyFragment"
        android:name="org.linphone.ui.main.history.fragment.HistoryFragment"
        android:label="HistoryFragment"
        tools:layout="@layout/history_fragment">
        <argument
            android:name="callId"
            app:argType="string" />
        <action
            android:id="@+id/action_historyFragment_to_emptyFragment"
            app:destination="@id/emptyFragment"
            app:popUpTo="@id/historyFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_historyFragment_to_conferenceConversationFragment"
            app:destination="@id/conferenceConversationFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right"
            app:launchSingleTop="true" />
    </fragment>

    <action
        android:id="@+id/action_global_historyFragment"
        app:destination="@id/historyFragment"
        app:exitAnim="@anim/slide_out_left"
        app:popExitAnim="@anim/slide_out_right"
        app:launchSingleTop="true" />

    <fragment
        android:id="@+id/conferenceConversationFragment"
        android:name="org.linphone.ui.main.history.fragment.ConferenceConversationFragment"
        android:label="ConferenceConversationFragment"
        tools:layout="@layout/chat_conversation_fragment">
        <argument
            android:name="conversationId"
            app:argType="string" />
    </fragment>

</navigation>