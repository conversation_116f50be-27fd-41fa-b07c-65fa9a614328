<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/meetings_nav_graph"
    app:startDestination="@id/emptyFragment">

    <fragment
        android:id="@+id/emptyFragment"
        android:name="org.linphone.ui.main.fragment.EmptyFragment"
        android:label="EmptyFragment"
        tools:layout="@layout/empty_fragment"/>

    <fragment
        android:id="@+id/meetingFragment"
        android:name="org.linphone.ui.main.meetings.fragment.MeetingFragment"
        android:label="MeetingFragment"
        tools:layout="@layout/meeting_fragment">
        <argument
            android:name="conferenceUri"
            app:argType="string" />
        <action
            android:id="@+id/action_meetingFragment_to_emptyFragment"
            app:destination="@id/emptyFragment"
            app:popUpTo="@id/meetingFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_meetingFragment_to_editMeetingFragment"
            app:destination="@id/editMeetingFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right"  />
    </fragment>

    <action
        android:id="@+id/action_global_meetingFragment"
        app:destination="@id/meetingFragment"
        app:launchSingleTop="true"/>

    <fragment
        android:id="@+id/editMeetingFragment"
        android:name="org.linphone.ui.main.meetings.fragment.EditMeetingFragment"
        android:label="EditMeetingFragment"
        tools:layout="@layout/meeting_edit_fragment">
        <argument
            android:name="conferenceUri"
            app:argType="string" />
        <action
            android:id="@+id/action_editMeetingFragment_to_addParticipantsFragment"
            app:destination="@id/addParticipantsFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <fragment
        android:id="@+id/addParticipantsFragment"
        android:name="org.linphone.ui.main.fragment.AddParticipantsFragment"
        android:label="AddParticipantsFragment"
        tools:layout="@layout/generic_add_participants_fragment">
        <argument
            android:name="selectedParticipants"
            app:argType="string[]"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>

</navigation>