<?xml version="1.0" encoding="utf-8"?>
<!--suppress ALL -->
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contact_nav_graph"
    app:startDestination="@id/emptyFragment">

    <fragment
        android:id="@+id/emptyFragment"
        android:name="org.linphone.ui.main.fragment.EmptyFragment"
        android:label="EmptyFragment"
        tools:layout="@layout/empty_fragment" />

    <fragment
        android:id="@+id/contactFragment"
        android:name="org.linphone.ui.main.contacts.fragment.ContactFragment"
        android:label="ContactFragment"
        tools:layout="@layout/contact_fragment" >
        <argument
            android:name="contactRef<PERSON>ey"
            app:argType="string" />
        <action
            android:id="@+id/action_contactFragment_to_editContactFragment"
            app:destination="@id/editContactFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right"
            app:launchSingleTop="true" />
        <action
            android:id="@+id/action_contactFragment_to_emptyFragment"
            app:destination="@id/emptyFragment"
            app:popUpTo="@id/contactFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <action
        android:id="@+id/action_global_contactFragment"
        app:destination="@id/contactFragment"
        app:launchSingleTop="true" />

    <fragment
        android:id="@+id/editContactFragment"
        android:name="org.linphone.ui.main.contacts.fragment.EditContactFragment"
        android:label="EditContactFragment"
        tools:layout="@layout/contact_new_or_edit_fragment" >
        <argument
            android:name="contactRefKey"
            app:argType="string" />
    </fragment>

</navigation>