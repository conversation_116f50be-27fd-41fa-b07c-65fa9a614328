<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.core.ConsolidatedPresence" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="menuClickListener"
            type="View.OnClickListener" />
        <variable
            name="copyPeerSipUriClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.history.viewmodel.HistoryViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_background_contrast_in_dark_mode">

            <ImageView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="15dp"
                android:adjustViewBounds="true"
                android:onClick="@{backClickListener}"
                android:visibility="@{viewModel.showBackButton ? View.VISIBLE : View.GONE}"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:tint="?attr/color_main1_500"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/title"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/main_page_title_style"
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="@dimen/top_bar_height"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:text="@string/history_title"
                app:layout_constraintEnd_toStartOf="@id/menu"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="parent"/>

            <ImageView
                android:onClick="@{menuClickListener}"
                android:id="@+id/menu"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="15dp"
                android:adjustViewBounds="true"
                android:src="@drawable/dots_three_vertical"
                android:contentDescription="@string/content_description_show_popup_menu"
                app:tint="?attr/color_main2_500"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/title" />

            <ScrollView
                android:id="@+id/scrollView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="?attr/color_grey_100"
                android:fillViewport="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.constraintlayout.widget.Barrier
                        android:id="@+id/actions_barrier"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="call, call_label, chat, chat_label, meeting, meeting_label"
                        app:barrierDirection="bottom" />

                    <include
                        android:id="@+id/avatar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        layout="@layout/contact_avatar_big"
                        bind:model="@{viewModel.callLogModel.avatarModel}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@{viewModel.callLogModel.avatarModel.name, default=`John Doe`}"
                        android:textColor="?attr/color_main2_800"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/avatar" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/address"
                        android:onClick="@{copyPeerSipUriClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@{viewModel.callLogModel.displayedAddress, default=`+33601020304`}"
                        android:textColor="?attr/color_main2_800"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:drawableEnd="@drawable/copy"
                        android:drawablePadding="5dp"
                        android:visibility="@{viewModel.isConferenceCallLog ? View.GONE : View.VISIBLE}"
                        app:drawableTint="?attr/color_main2_600"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/name" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_300"
                        android:id="@+id/status"
                        android:visibility="@{viewModel.callLogModel.avatarModel.presenceStatus == ConsolidatedPresence.Offline ? View.GONE : View.VISIBLE}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.callLogModel.avatarModel.lastPresenceInfo, default=@string/contact_presence_status_online}"
                        android:textColor="@{viewModel.callLogModel.avatarModel.presenceStatus == ConsolidatedPresence.Online ? @color/success_500 : @color/warning_600, default=@color/success_500}"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/address" />

                    <ImageView
                        android:id="@+id/call"
                        android:onClick="@{() -> viewModel.startAudioCall()}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/phone"
                        android:contentDescription="@string/content_description_call_start"
                        android:visibility="@{viewModel.isConferenceCallLog ? View.GONE : View.VISIBLE}"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintEnd_toStartOf="@id/chat"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/call_label"
                        android:onClick="@{() -> viewModel.startAudioCall()}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_call_action"
                        android:textSize="14sp"
                        android:visibility="@{viewModel.isConferenceCallLog ? View.GONE : View.VISIBLE}"
                        app:layout_constraintTop_toBottomOf="@id/call"
                        app:layout_constraintStart_toStartOf="@id/call"
                        app:layout_constraintEnd_toEndOf="@id/call"/>

                    <ImageView
                        android:id="@+id/chat"
                        android:onClick="@{() -> viewModel.goToConversation()}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/envelope_simple"
                        android:contentDescription="@string/content_description_chat_create"
                        android:visibility="@{viewModel.chatDisabled || viewModel.isConferenceCallLog ? View.GONE : View.VISIBLE}"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintEnd_toStartOf="@id/meeting"
                        app:layout_constraintStart_toEndOf="@id/call"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/chat_label"
                        android:onClick="@{() -> viewModel.goToConversation()}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_message_action"
                        android:textSize="14sp"
                        android:visibility="@{viewModel.chatDisabled || viewModel.isConferenceCallLog ? View.GONE : View.VISIBLE}"
                        app:layout_constraintTop_toBottomOf="@id/chat"
                        app:layout_constraintStart_toStartOf="@id/chat"
                        app:layout_constraintEnd_toEndOf="@id/chat"/>


                    <ImageView
                        android:id="@+id/meeting"
                        android:onClick="@{() -> viewModel.goToMeetingWaitingRoom()}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/users_three"
                        android:contentDescription="@string/content_description_join_conference"
                        android:visibility="@{viewModel.isConferenceCallLog ? View.VISIBLE : View.GONE, default=gone}"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/meeting_chat"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/meeting_label"
                        android:onClick="@{() -> viewModel.goToMeetingWaitingRoom()}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/meeting_waiting_room_join"
                        android:textSize="14sp"
                        android:visibility="@{viewModel.isConferenceCallLog ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/meeting"
                        app:layout_constraintStart_toStartOf="@id/meeting"
                        app:layout_constraintEnd_toEndOf="@id/meeting"/>

                    <ImageView
                        android:id="@+id/meeting_chat"
                        android:onClick="@{() -> viewModel.goToMeetingConversation()}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/envelope_simple"
                        android:contentDescription="@string/content_description_go_to_conversation"
                        android:visibility="@{viewModel.isConferenceCallLog &amp;&amp; viewModel.isChatRoomAvailable ? View.VISIBLE : View.GONE, default=gone}"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintStart_toEndOf="@id/meeting"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/meeting_chat_label"
                        android:onClick="@{() -> viewModel.goToMeetingConversation()}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/history_group_call_go_to_conversation"
                        android:textSize="14sp"
                        android:visibility="@{viewModel.isConferenceCallLog &amp;&amp; viewModel.isChatRoomAvailable ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/meeting_chat"
                        app:layout_constraintStart_toStartOf="@id/meeting_chat"
                        app:layout_constraintEnd_toEndOf="@id/meeting_chat"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/call_history"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="28dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="@dimen/screen_bottom_margin"
                        android:background="@drawable/shape_squircle_white_background"
                        android:nestedScrollingEnabled="true"
                        app:layout_constraintVertical_bias="0"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/actions_barrier"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/operation_in_progress"
            bind:visibility="@{viewModel.operationInProgress}" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>