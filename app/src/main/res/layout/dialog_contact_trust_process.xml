<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="dismissClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.contacts.model.ContactTrustDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{dismissClickListener}"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/dialog_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="2dp"
            android:src="@drawable/shape_dialog_background"
            android:contentDescription="@null"
            app:layout_constraintWidth_max="@dimen/dialog_max_width"
            app:layout_constraintBottom_toBottomOf="@id/anchor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/section_header_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingTop="@dimen/dialog_top_bottom_margin"
            android:text="@string/contact_dialog_devices_trust_help_title"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/message"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginTop="10dp"
            android:text="@string/contact_dialog_devices_trust_help_message"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@id/image1"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <include
            android:id="@+id/image1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="32dp"
            layout="@layout/contact_avatar"
            bind:model="@{viewModel.avatarModel}"
            bind:hidePresence="@{true}"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/arrow"
            app:layout_constraintTop_toBottomOf="@id/message"
            app:layout_constraintBottom_toTopOf="@id/confirm" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/arrow_right"
            android:contentDescription="@null"
            app:tint="?attr/color_main2_600"
            app:layout_constraintStart_toEndOf="@id/image1"
            app:layout_constraintEnd_toStartOf="@id/image2"
            app:layout_constraintTop_toTopOf="@id/image1"
            app:layout_constraintBottom_toBottomOf="@id/image1"/>

        <include
            android:id="@+id/image2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            layout="@layout/contact_avatar"
            bind:model="@{viewModel.trustedAvatarModel}"
            bind:hidePresence="@{true}"
            app:strokeWidth="@dimen/avatar_trust_border_width"
            app:strokeColor="?attr/color_info_500"
            app:layout_constraintStart_toEndOf="@id/arrow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/image1"
            app:layout_constraintBottom_toBottomOf="@id/image1"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{dismissClickListener}"
            style="@style/primary_button_label_style"
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/dialog_ok"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/image1"
            app:layout_constraintBottom_toTopOf="@id/anchor"/>

        <View
            android:id="@+id/anchor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dialog_top_bottom_margin"
            app:layout_constraintTop_toBottomOf="@id/confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>