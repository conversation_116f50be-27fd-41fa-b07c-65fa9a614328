<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <import type="android.view.View" />
        <variable
            name="addToContactsListener"
            type="View.OnClickListener" />
        <variable
            name="goToContactClickListener"
            type="View.OnClickListener" />
        <variable
            name="copyNumberClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteClickListener"
            type="View.OnClickListener" />
        <variable
            name="contactExists"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_separator">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/add_to_contact"
            android:onClick="@{addToContactsListener}"
            android:visibility="@{contactExists ? View.GONE : View.VISIBLE}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/menu_add_address_to_contacts"
            style="@style/context_menu_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/user_plus"
            app:layout_constraintBottom_toTopOf="@id/go_to_contact"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/go_to_contact"
            android:onClick="@{goToContactClickListener}"
            android:visibility="@{contactExists ? View.VISIBLE : View.GONE}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/menu_see_existing_contact"
            style="@style/context_menu_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/user_circle"
            app:layout_constraintBottom_toTopOf="@id/share"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/share"
            android:onClick="@{copyNumberClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/menu_copy_sip_address"
            style="@style/context_menu_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/copy"
            app:layout_constraintBottom_toTopOf="@id/delete"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/delete"
            android:onClick="@{deleteClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/menu_delete_selected_item"
            style="@style/context_menu_danger_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/trash_simple"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>