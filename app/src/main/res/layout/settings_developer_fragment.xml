<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <ImageView
            android:id="@+id/back"
            android:onClick="@{backClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_developer_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:background="?attr/color_grey_100"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:onClick="@{() -> viewModel.toggleDeveloperSettings()}"
                    android:id="@+id/settings_developer_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_developer_show_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/settings_developer_switch"
                    app:layout_constraintBottom_toBottomOf="@id/settings_developer_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/settings_developer_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/settings_developer_switch"
                    android:onClick="@{() -> viewModel.toggleDeveloperSettings()}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@{viewModel.showDeveloperSettings}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:onClick="@{() -> viewModel.toggleLogcat()}"
                    android:id="@+id/logcat_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_developer_print_logs_in_logcat"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/logcat_switch"
                    app:layout_constraintBottom_toBottomOf="@id/logcat_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/logcat_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/logcat_switch"
                    android:onClick="@{() -> viewModel.toggleLogcat()}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@{viewModel.logcat}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/settings_developer_switch" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/upload_server_url_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:text="@string/settings_advanced_upload_server_url"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/logcat_switch"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/upload_server_url"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.fileSharingServerUrl}"
                    android:textSize="14sp"
                    android:maxLines="1"
                    android:background="@drawable/edit_text_background"
                    android:inputType="text|textUri"
                    android:hint="@string/settings_advanced_upload_server_url"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintWidth_max="@dimen/text_input_max_width"
                    app:layout_constraintTop_toBottomOf="@id/upload_server_url_label"
                    app:layout_constraintStart_toStartOf="@id/upload_server_url_label"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/logs_upload_server_url_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:text="@string/settings_advanced_logs_upload_server_url"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/upload_server_url"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/logs_upload_server_url"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.logsSharingServerUrl}"
                    android:textSize="14sp"
                    android:maxLines="1"
                    android:background="@drawable/edit_text_background"
                    android:inputType="text|textUri"
                    android:hint="@string/settings_advanced_logs_upload_server_url"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintWidth_max="@dimen/text_input_max_width"
                    app:layout_constraintTop_toBottomOf="@id/logs_upload_server_url_label"
                    app:layout_constraintStart_toStartOf="@id/logs_upload_server_url_label"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:onClick="@{() -> viewModel.toggleConferencesEndToEndEncryption()}"
                    android:id="@+id/e2e_encrypted_conferences_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_advanced_create_e2e_encrypted_conferences_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/e2e_encrypted_conferences_switch"
                    app:layout_constraintBottom_toBottomOf="@id/e2e_encrypted_conferences_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/e2e_encrypted_conferences_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/e2e_encrypted_conferences_switch"
                    android:onClick="@{() -> viewModel.toggleConferencesEndToEndEncryption()}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@{viewModel.createEndToEndEncryptedConferences}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/logs_upload_server_url" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>