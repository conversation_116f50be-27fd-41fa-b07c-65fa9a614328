<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="mergeCallsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CallsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <ImageView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="15dp"
            android:adjustViewBounds="true"
            android:onClick="@{backClickListener}"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@string/calls_list_title"
            app:layout_constraintEnd_toStartOf="@id/merge"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/merge"
            android:onClick="@{mergeCallsClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="15dp"
            android:adjustViewBounds="true"
            android:src="@drawable/arrows_merge"
            android:contentDescription="@string/content_description_merge_calls_into_conference"
            android:visibility="@{viewModel.callsCount > 1 ? View.VISIBLE : View.GONE}"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"/>

        <View
            android:id="@+id/background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/color_main2_000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/back" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/calls_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>