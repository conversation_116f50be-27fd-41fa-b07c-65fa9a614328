<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.main.recordings.model.RecordingModel" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="onLongClickListener"
            type="View.OnLongClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cardview"
            android:onClick="@{onClickListener}"
            android:onLongClick="@{onLongClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="8dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:background="@drawable/primary_cell_r10_background"
            android:elevation="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_700"
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="5dp"
                android:text="@{model.displayName, default=`John Doe`}"
                android:textSize="13sp"
                android:textColor="?attr/color_main2_600"
                android:maxLines="1"
                android:ellipsize="end"
                android:drawableStart="@drawable/phone"
                android:drawablePadding="8dp"
                android:drawableTint="?attr/color_main2_600"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/play"/>

            <ImageView
                android:id="@+id/play"
                android:onClick="@{onClickListener}"
                android:layout_width="@dimen/big_icon_size"
                android:layout_height="@dimen/big_icon_size"
                android:layout_marginEnd="10dp"
                android:padding="12dp"
                android:src="@drawable/play_fill"
                android:contentDescription="@string/content_description_play_call_recording"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:tint="?attr/color_main1_500" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style"
                android:id="@+id/time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="@{model.dateTime, default=`15 Mai - 14h38`}"
                android:textSize="14sp"
                android:textColor="?attr/color_main2_500"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/duration"
                app:layout_constraintBottom_toBottomOf="@id/duration"
                app:layout_constraintEnd_toStartOf="@id/duration" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_300"
                android:id="@+id/duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="10dp"
                android:text="@{model.formattedDuration, default=`00:42`}"
                android:textSize="12sp"
                android:textColor="?attr/color_main2_500"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintTop_toBottomOf="@id/play"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/play"
                app:layout_constraintEnd_toEndOf="@id/play" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>