<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="onLongClickListener"
            type="View.OnLongClickListener" />
        <variable
            name="model"
            type="org.linphone.ui.call.model.CallModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{onClickListener}"
        android:onLongClick="@{onLongClickListener}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/primary_cell_background">

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            layout="@layout/contact_avatar"
            bind:model="@{model.contact}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.displayName, default=`John Doe`}"
            android:textSize="14sp"
            android:layout_marginStart="10dp"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/call_state_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="5dp"
            android:contentDescription="@null"
            android:src="@{model.isPaused ? @drawable/phone_pause : @drawable/phone_call, default=@drawable/phone_pause}"
            app:tint="?attr/color_main2_500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/call_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="@{model.state, default=`Paused`}"
            android:textSize="12sp"
            android:layout_marginStart="10dp"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/name"
            app:layout_constraintEnd_toStartOf="@id/call_state_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>