<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="android.text.InputType" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.LdapViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_main2_000">

        <ImageView
            android:id="@+id/back"
            android:onClick="@{backClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:text="@{viewModel.isEdit ? @string/settings_contacts_edit_ldap_server_title : @string/settings_contacts_add_ldap_server_title, default=@string/settings_contacts_add_ldap_server_title}"
            app:layout_constraintEnd_toStartOf="@id/delete"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/delete"
            android:onClick="@{() -> viewModel.delete()}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/trash_simple"
            android:contentDescription="@string/content_description_ldap_delete"
            android:visibility="@{viewModel.isEdit ? View.VISIBLE : View.GONE, default=gone}"
            app:tint="?attr/color_main1_500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="@id/back"
            app:layout_constraintBottom_toBottomOf="@id/back"/>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:background="?attr/color_grey_100"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:onClick="@{() -> viewModel.toggleEnabled()}"
                    android:id="@+id/enabled_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_contacts_ldap_enabled_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/enabled_switch"
                    app:layout_constraintBottom_toBottomOf="@id/enabled_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/enabled_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/enabled_switch"
                    android:onClick="@{() -> viewModel.toggleEnabled()}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@{viewModel.isEnabled}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/server_url_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_server_url_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/enabled_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/server_url"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.serverUrl}"
                    android:inputType="text|textUri"
                    android:hint="@string/settings_contacts_ldap_server_url_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/server_url_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/bind_dn_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_bind_dn_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/server_url"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/bind_dn"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.bindDn}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_bind_dn_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bind_dn_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/password_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_password_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/bind_dn"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/password"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.password}"
                    android:hint="@string/password"
                    passwordInputType="@{viewModel.showPassword ? InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD : InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD, default=textPassword}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/password_title" />

                <ImageView
                    android:onClick="@{() -> viewModel.toggleShowPassword()}"
                    android:id="@+id/eye"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="0dp"
                    android:padding="4dp"
                    android:layout_marginEnd="20dp"
                    android:contentDescription="@string/content_description_toggle_password_visibility"
                    android:src="@{viewModel.showPassword ? @drawable/eye_slash : @drawable/eye, default=@drawable/eye}"
                    app:tint="?attr/color_main2_500"
                    app:layout_constraintEnd_toEndOf="@id/password"
                    app:layout_constraintTop_toTopOf="@id/password"
                    app:layout_constraintBottom_toBottomOf="@id/password" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:onClick="@{() -> viewModel.toggleTls()}"
                    android:id="@+id/tls_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_contacts_ldap_use_tls_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/tls_switch"
                    app:layout_constraintBottom_toBottomOf="@id/tls_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tls_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/tls_switch"
                    android:onClick="@{() -> viewModel.toggleTls()}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@{viewModel.useTls}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/password" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/search_base_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_search_base_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/tls_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/search_base"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.searchBase}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_search_base_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/search_base_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/search_filter_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_search_filter_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/search_base"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/search_filter"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.searchFilter}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_search_filter_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/search_filter_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/max_results_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_max_results_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/search_filter"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/max_results"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.maxResults}"
                    android:inputType="numberSigned"
                    android:hint="@string/settings_contacts_ldap_max_results_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/max_results_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/request_timeout_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_request_timeout_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/max_results"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/request_timeout"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.requestTimeout}"
                    android:inputType="numberSigned"
                    android:hint="@string/settings_contacts_ldap_request_timeout_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/request_timeout_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/request_delay_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_request_delay_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/request_timeout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/request_delay"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.requestDelay}"
                    android:inputType="numberSigned"
                    android:hint="@string/settings_contacts_ldap_request_delay_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/request_delay_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/min_chars_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_min_characters_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/request_delay"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/min_chars"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.minCharacters}"
                    android:inputType="numberSigned"
                    android:hint="@string/settings_contacts_ldap_min_characters_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/min_chars_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/name_attributes_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_name_attributes_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/min_chars"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/name_attributes"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.nameAttributes}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_name_attributes_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/name_attributes_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/sip_attributes_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_sip_attributes_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/name_attributes"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/sip_attributes"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.sipAttributes}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_sip_attributes_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/sip_attributes_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/sip_domain_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_ldap_sip_domain_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/sip_attributes"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/sip_domain"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginBottom="@dimen/screen_bottom_margin"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.sipDomain}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_ldap_sip_domain_title"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/sip_domain_title"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/add_server"
            android:onClick="@{() -> viewModel.addServer()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="16dp"
            android:contentDescription="@string/content_description_ldap_save"
            android:src="@{viewModel.isEdit ? @drawable/check : @drawable/plus_circle, default=@drawable/plus_circle}"
            app:tint="?attr/color_on_main"
            app:backgroundTint="?attr/color_main1_500"
            app:shapeAppearanceOverlay="@style/rounded"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>