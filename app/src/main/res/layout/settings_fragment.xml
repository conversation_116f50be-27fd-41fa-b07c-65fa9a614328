<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="turnOnVfsClickListener"
            type="View.OnClickListener" />
        <variable
            name="advancedSettingsClickListener"
            type="View.OnClickListener" />
        <variable
            name="developerSettingsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <ImageView
            android:id="@+id/back"
            android:onClick="@{backClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:background="?attr/color_grey_100"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/security"
                    android:onClick="@{() -> viewModel.toggleSecurityExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_security_title"
                    android:drawableEnd="@{viewModel.expandSecurity ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <include
                    android:id="@+id/security_settings"
                    layout="@layout/settings_security"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandSecurity ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/security"
                    bind:turnOnVfsClickListener="@{turnOnVfsClickListener}"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/calls"
                    android:onClick="@{() -> viewModel.toggleCallsExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_calls_title"
                    android:drawableEnd="@{viewModel.expandCalls ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/security_settings"/>

                <include
                    android:id="@+id/calls_settings"
                    layout="@layout/settings_calls"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandCalls ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/calls"
                    bind:viewModel="@{viewModel}"/>


                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/contacts"
                    android:onClick="@{() -> viewModel.toggleContactsExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_contacts_title"
                    android:drawableEnd="@{viewModel.expandContacts ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    android:visibility="@{viewModel.showContactsSettings ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/calls_settings"/>

                <include
                    android:id="@+id/contacts_settings"
                    layout="@layout/settings_contacts"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandContacts &amp;&amp; viewModel.showContactsSettings ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/contacts"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/meetings"
                    android:onClick="@{() -> viewModel.toggleMeetingsExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_meetings_title"
                    android:drawableEnd="@{viewModel.expandMeetings ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    android:visibility="@{viewModel.showMeetingsSettings ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/contacts_settings"/>

                <include
                    android:id="@+id/meetings_settings"
                    layout="@layout/settings_meetings"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandMeetings &amp;&amp; viewModel.showMeetingsSettings ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/meetings"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/network"
                    android:onClick="@{() -> viewModel.toggleNetworkExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_network_title"
                    android:drawableEnd="@{viewModel.expandNetwork ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/meetings_settings"/>

                <include
                    android:id="@+id/network_settings"
                    layout="@layout/settings_network"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandNetwork ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/network"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/user_interface"
                    android:onClick="@{() -> viewModel.toggleUserInterfaceExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_user_interface_title"
                    android:drawableEnd="@{viewModel.expandUserInterface ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/network_settings"/>

                <include
                    android:id="@+id/user_interface_settings"
                    layout="@layout/settings_user_interface"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.expandUserInterface ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/user_interface"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/tunnel"
                    android:onClick="@{() -> viewModel.toggleTunnelExpand()}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_tunnel_title"
                    android:visibility="@{viewModel.isTunnelAvailable ? View.VISIBLE : View.GONE}"
                    android:drawableEnd="@{viewModel.expandTunnel ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_interface_settings"/>

                <include
                    android:id="@+id/tunnel_settings"
                    layout="@layout/settings_tunnel"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:visibility="@{viewModel.isTunnelAvailable &amp;&amp; viewModel.expandTunnel ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintTop_toBottomOf="@id/tunnel"
                    bind:viewModel="@{viewModel}"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/advanced_settings"
                    android:onClick="@{advancedSettingsClickListener}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="@dimen/screen_bottom_margin"
                    android:text="@string/settings_advanced_title"
                    android:drawableEnd="@drawable/caret_right"
                    android:drawableTint="?attr/color_main2_600"
                    android:visibility="@{viewModel.showAdvancedSettings ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tunnel_settings"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/developer_settings"
                    android:onClick="@{developerSettingsClickListener}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:layout_marginStart="26dp"
                    android:layout_marginEnd="26dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="@dimen/screen_bottom_margin"
                    android:text="@string/settings_developer_title"
                    android:drawableEnd="@drawable/caret_right"
                    android:drawableTint="?attr/color_main2_600"
                    android:visibility="@{viewModel.showDeveloperSettings ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/advanced_settings"
                    app:layout_constraintBottom_toBottomOf="parent"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>