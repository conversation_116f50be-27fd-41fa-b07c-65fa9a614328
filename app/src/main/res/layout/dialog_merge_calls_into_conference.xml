<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.utils.ConfirmationDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{() -> viewModel.dismiss()}"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/dialog_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="2dp"
            android:src="@drawable/shape_dialog_background"
            android:contentDescription="@null"
            app:layout_constraintWidth_max="@dimen/dialog_max_width"
            app:layout_constraintBottom_toBottomOf="@id/anchor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/section_header_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingTop="@dimen/dialog_top_bottom_margin"
            android:text="@string/calls_list_dialog_merge_into_conference_title"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.dismiss()}"
            style="@style/secondary_button_label_style"
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/dialog_cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toTopOf="@id/confirm"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.confirm()}"
            style="@style/primary_button_label_style"
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/calls_list_dialog_merge_into_conference_label"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/cancel"
            app:layout_constraintBottom_toTopOf="@id/anchor"/>

        <View
            android:id="@+id/anchor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dialog_top_bottom_margin"
            app:layout_constraintTop_toBottomOf="@id/confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>