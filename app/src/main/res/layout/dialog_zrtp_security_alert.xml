<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.model.ZrtpAlertDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_zrtp_dialog_error_header_background"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/body">

            <ImageView
                android:id="@+id/header_icon"
                android:layout_width="@dimen/icon_size"
                android:layout_height="@dimen/icon_size"
                android:layout_marginTop="10dp"
                android:src="@drawable/shield_warning"
                android:contentDescription="@null"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:tint="@color/bc_white" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_700"
                android:id="@id/header_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:paddingBottom="10dp"
                android:text="@string/call_dialog_zrtp_security_alert_title"
                android:textSize="14sp"
                android:textColor="@color/bc_white"
                app:layout_constraintTop_toBottomOf="@id/header_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_zrtp_dialog_error_background"
            app:layout_constraintTop_toBottomOf="@id/header"
            app:layout_constraintBottom_toBottomOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style"
                android:id="@+id/message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:layout_marginTop="10dp"
                android:text="@string/call_dialog_zrtp_security_alert_message"
                android:textSize="14sp"
                android:textColor="@color/gray_main2_600"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_600"
                android:id="@+id/try_again"
                android:onClick="@{() -> viewModel.tryAgain()}"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                android:gravity="center"
                android:background="@drawable/shape_red_outlined_button_background"
                android:text="@string/call_dialog_zrtp_security_alert_try_again"
                android:textSize="18sp"
                android:textColor="?attr/color_danger_500"
                android:maxLines="1"
                android:ellipsize="end"
                android:visibility="@{viewModel.allowTryAgain ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintWidth_max="@dimen/button_max_width"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/message"
                app:layout_constraintBottom_toTopOf="@id/hang_up"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_600"
                android:id="@+id/hang_up"
                android:onClick="@{() -> viewModel.hangUp()}"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="15dp"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                android:gravity="center"
                android:background="@drawable/shape_red_button_background"
                android:text="@string/call_action_hang_up"
                android:textSize="18sp"
                android:textColor="@color/bc_white"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintWidth_max="@dimen/button_max_width"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/try_again"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>