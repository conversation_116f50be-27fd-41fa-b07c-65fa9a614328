<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
        <variable
            name="conferenceViewModel"
            type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_900">

        <ScrollView
            android:id="@+id/grid_scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.gridlayout.widget.GridLayout
                android:id="@+id/grid_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                entries="@{conferenceViewModel.participantDevices}"
                layout="@{@layout/call_conference_audio_only_cell}"
                app:columnCount="2"
                app:orientation="horizontal" />

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>