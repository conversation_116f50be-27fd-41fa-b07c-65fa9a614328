<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.main.settings.model.CardDavLdapModel" />
    </data>

    <androidx.appcompat.widget.AppCompatTextView
        style="@style/settings_title_style"
        android:id="@+id/name"
        android:onClick="@{() -> model.clicked()}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="10dp"
        android:padding="5dp"
        android:text="@{model.name, default=`LDAP or CardDAV item`}"
        android:maxLines="2"
        android:ellipsize="end"
        android:drawableEnd="@drawable/pencil_simple"
        android:drawableTint="?attr/color_main2_600"
        app:layout_constraintStart_toEndOf="@id/avatar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</layout>