<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <import type="android.view.View" />
        <variable
            name="favoriteClickListener"
            type="View.OnClickListener" />
        <variable
            name="shareClickListener"
            type="View.OnClickListener" />
        <variable
            name="inviteClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteClickListener"
            type="View.OnClickListener" />
        <variable
            name="isFavourite"
            type="Boolean" />
        <variable
            name="isStored"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_separator">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/context_menu_action_label_style"
            android:id="@+id/favorite"
            android:onClick="@{favoriteClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:background="@drawable/menu_item_background"
            android:visibility="@{isStored ? View.VISIBLE : View.GONE}"
            android:text="@{isFavourite ? @string/contact_details_remove_from_favourites : @string/contact_details_add_to_favourites, default=@string/contact_details_add_to_favourites}"
            android:drawableStart="@{isFavourite ? @drawable/heart_fill : @drawable/heart, default=@drawable/heart_fill}"
            android:drawableTint="@{isFavourite ? @color/danger_500 : @color/main2_500, default=@color/main2_500}"
            app:layout_constraintBottom_toTopOf="@id/share"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/context_menu_action_label_style"
            android:id="@+id/share"
            android:onClick="@{shareClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/contact_details_share"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/share_network"
            app:layout_constraintBottom_toTopOf="@id/delete"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/context_menu_danger_action_label_style"
            android:id="@+id/delete"
            android:onClick="@{deleteClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/contact_details_delete"
            android:visibility="@{isStored ? View.VISIBLE : View.GONE}"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/trash_simple"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>