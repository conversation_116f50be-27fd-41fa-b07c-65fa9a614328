<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:background="@drawable/shape_squircle_white_background">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:onClick="@{() -> viewModel.addLdapServer()}"
            android:id="@+id/add_ldap_server"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="20dp"
            android:padding="5dp"
            android:text="@string/settings_contacts_add_ldap_server_title"
            android:maxLines="2"
            android:ellipsize="end"
            android:drawableEnd="@drawable/caret_right"
            android:drawableTint="?attr/color_main2_600"
            android:visibility="@{viewModel.ldapAvailable ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/existing_ldap_servers"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <LinearLayout
            android:id="@+id/existing_ldap_servers"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="6dp"
            android:orientation="vertical"
            android:visibility="@{viewModel.ldapServers.size() == 0 ? View.GONE : View.VISIBLE, default=gone}"
            app:entries="@{viewModel.ldapServers}"
            app:layout="@{@layout/settings_contacts_carddav_ldap_list_cell}"
            app:layout_constraintTop_toBottomOf="@id/add_ldap_server"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:onClick="@{() -> viewModel.addCardDavServer()}"
            android:id="@+id/add_carddav_server"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="16dp"
            android:padding="5dp"
            android:text="@string/settings_contacts_add_carddav_server_title"
            android:maxLines="2"
            android:ellipsize="end"
            android:drawableEnd="@drawable/caret_right"
            android:drawableTint="?attr/color_main2_600"
            app:layout_constraintTop_toBottomOf="@id/existing_ldap_servers"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <LinearLayout
            android:id="@+id/existing_carddav_servers"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="6dp"
            android:orientation="vertical"
            android:visibility="@{viewModel.cardDavFriendsLists.size() == 0 ? View.GONE : View.VISIBLE, default=gone}"
            app:entries="@{viewModel.cardDavFriendsLists}"
            app:layout="@{@layout/settings_contacts_carddav_ldap_list_cell}"
            app:layout_constraintTop_toBottomOf="@id/add_carddav_server"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>