<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.main.settings.model.CodecModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:onClick="@{() -> model.toggleEnabled()}"
            android:id="@+id/codec_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@{model.mimeType, default=`Opus`}"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintTop_toTopOf="@id/codec_switch"
            app:layout_constraintBottom_toTopOf="@id/codec_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/codec_switch"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_subtitle_style"
            android:onClick="@{() -> model.toggleEnabled()}"
            android:id="@+id/codec_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@{model.subtitle, default=`48000 Hz`}"
            android:maxLines="1"
            android:ellipsize="end"
            android:visibility="@{model.subtitle.isEmpty() ? View.GONE : View.VISIBLE}"
            app:layout_constraintTop_toBottomOf="@id/codec_title"
            app:layout_constraintBottom_toBottomOf="@id/codec_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/codec_switch"/>

        <com.google.android.material.materialswitch.MaterialSwitch
            style="@style/material_switch_style"
            android:id="@+id/codec_switch"
            android:onClick="@{() -> model.toggleEnabled()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:checked="@{model.enabled}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>