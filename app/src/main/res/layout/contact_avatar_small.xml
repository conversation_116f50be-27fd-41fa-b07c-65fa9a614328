<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.core.ConsolidatedPresence" />
        <import type="org.linphone.core.SecurityLevel" />
        <variable
            name="model"
            type="org.linphone.contacts.AbstractAvatarModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            style="@style/avatar_imageview"
            android:id="@+id/avatar"
            android:layout_width="@dimen/avatar_bubble_size"
            android:layout_height="@dimen/avatar_bubble_size"
            android:contentDescription="@null"
            coilBubbleAvatar="@{model}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/trust_overlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:src="@{model.trust == SecurityLevel.Unsafe ? @drawable/shape_circle_unsafe_contact_overlay : @drawable/shape_circle_trusted_contact_overlay, default=@drawable/shape_circle_trusted_contact_overlay}"
            android:visibility="@{model.showTrust &amp;&amp; (model.trust == SecurityLevel.EndToEndEncryptedAndVerified || model.trust == SecurityLevel.Unsafe) ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintEnd_toEndOf="@id/avatar"
            app:layout_constraintStart_toStartOf="@id/avatar"
            app:layout_constraintTop_toTopOf="@id/avatar"
            app:layout_constraintBottom_toBottomOf="@id/avatar"/>

        <ImageView
            android:id="@+id/presence_badge"
            android:layout_width="@dimen/avatar_bubble_presence_badge_size"
            android:layout_height="@dimen/avatar_bubble_presence_badge_size"
            android:layout_marginEnd="1dp"
            android:background="@drawable/led_background"
            android:padding="1dp"
            android:contentDescription="@{model.presenceStatus == ConsolidatedPresence.Online ? @string/content_description_contact_online : @string/content_description_contact_away}"
            app:presenceIcon="@{model.presenceStatus}"
            app:layout_constraintEnd_toEndOf="@id/avatar"
            app:layout_constraintBottom_toBottomOf="@id/avatar" />

        <ImageView
            android:id="@+id/trust_badge"
            android:layout_width="@dimen/avatar_bubble_presence_badge_size"
            android:layout_height="@dimen/avatar_bubble_presence_badge_size"
            android:layout_marginStart="1dp"
            android:src="@{model.trust == SecurityLevel.EndToEndEncryptedAndVerified ? @drawable/trusted : @drawable/not_trusted, default=@drawable/trusted}"
            android:visibility="@{model.showTrust &amp;&amp; (model.trust == SecurityLevel.EndToEndEncryptedAndVerified || model.trust == SecurityLevel.Unsafe) ? View.VISIBLE : View.GONE, default=gone}"
            android:contentDescription="@{model.trust == SecurityLevel.EndToEndEncryptedAndVerified ? @string/content_description_trusted_contact_icon : @string/content_description_not_trusted_contact_icon}"
            app:layout_constraintStart_toStartOf="@id/avatar"
            app:layout_constraintBottom_toBottomOf="@id/avatar"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>