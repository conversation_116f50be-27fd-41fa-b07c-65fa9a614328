<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CallsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@{viewModel.callsCount > 1 || viewModel.showTopBar ? @drawable/color_success_500 : @drawable/color_black, default=@drawable/color_black}"
        android:onClick="@{() -> viewModel.topBarClicked()}">

        <ImageView
            android:id="@+id/call_icon"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:src="@{viewModel.callsTopBarIcon, default=@drawable/phone_pause}"
            android:contentDescription="@null"
            android:visibility="@{viewModel.callsCount > 1 || viewModel.showTopBar ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/call_display_name"
            app:layout_constraintBottom_toBottomOf="@id/call_display_name"
            app:tint="@color/bc_white" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_800"
            android:id="@+id/call_display_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:gravity="center_vertical"
            android:text="@{viewModel.callsTopBarLabel, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="16sp"
            android:visibility="@{viewModel.callsCount > 1 || viewModel.showTopBar ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintEnd_toStartOf="@id/call_time"
            app:layout_constraintStart_toEndOf="@id/call_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/call_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:gravity="center_vertical"
            android:text="@{viewModel.callsTopBarStatus, default=`Paused`}"
            android:textColor="@color/bc_white"
            android:textSize="14sp"
            android:visibility="@{viewModel.callsCount > 1 || viewModel.showTopBar ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/call_display_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>