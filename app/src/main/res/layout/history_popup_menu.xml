<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="addToContactsListener"
            type="View.OnClickListener" />
        <variable
            name="goToContactListener"
            type="View.OnClickListener" />
        <variable
            name="copyNumberClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteAllHistoryClickListener"
            type="View.OnClickListener" />
        <variable
            name="contactExists"
            type="Boolean" />
        <variable
            name="isConferenceCallLog"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/popup_menu_padding"
        android:paddingStart="@dimen/popup_menu_padding"
        android:background="@drawable/shape_round_popup_menu_background">

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{addToContactsListener}"
            android:visibility="@{contactExists || isConferenceCallLog ? View.GONE : View.VISIBLE, default=gone}"
            style="@style/default_text_style"
            android:id="@+id/add_to_contact"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/popup_menu_item_top_margin"
            android:gravity="start|center_vertical"
            android:text="@string/menu_add_address_to_contacts"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:drawableStart="@drawable/plus_circle"
            android:drawablePadding="5dp"
            app:drawableTint="?attr/color_main2_700"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/go_to_contact"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{goToContactListener}"
            android:visibility="@{contactExists &amp;&amp; !isConferenceCallLog ? View.VISIBLE : View.GONE}"
            style="@style/default_text_style"
            android:id="@+id/go_to_contact"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/popup_menu_item_top_margin"
            android:gravity="start|center_vertical"
            android:text="@string/menu_see_existing_contact"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:drawableStart="@drawable/user_circle"
            android:drawablePadding="5dp"
            app:drawableTint="?attr/color_main2_700"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/add_to_contact"
            app:layout_constraintBottom_toTopOf="@id/copy_number"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{copyNumberClickListener}"
            style="@style/default_text_style"
            android:id="@+id/copy_number"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:text="@string/menu_copy_sip_address"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:drawableStart="@drawable/copy"
            android:drawablePadding="5dp"
            app:drawableTint="?attr/color_main2_700"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/go_to_contact"
            app:layout_constraintBottom_toTopOf="@id/delete_history"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{deleteAllHistoryClickListener}"
            style="@style/default_text_style"
            android:id="@+id/delete_history"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/popup_menu_item_top_margin"
            android:gravity="start|center_vertical"
            android:text="@string/menu_delete_history"
            android:textSize="14sp"
            android:textColor="?attr/color_danger_500"
            android:drawableStart="@drawable/trash_simple"
            android:drawablePadding="5dp"
            app:drawableTint="?attr/color_danger_500"
            app:layout_constraintWidth_min="170dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/copy_number"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/bottom_anchor"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_marginBottom="@dimen/popup_menu_item_top_margin"
            app:layout_constraintWidth_max="@dimen/popup_menu_width"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/delete_history"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>