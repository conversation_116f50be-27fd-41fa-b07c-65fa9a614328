<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="addParticipantsClickListener"
            type="View.OnClickListener" />
        <variable
            name="showMenuClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <ImageView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="15dp"
            android:adjustViewBounds="true"
            android:onClick="@{backClickListener}"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@{viewModel.conferenceModel.participantsLabel, default=`Participant (1)`}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/show_menu"
            android:onClick="@{showMenuClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/top_bar_height"
            android:padding="15dp"
            android:adjustViewBounds="true"
            android:src="@drawable/dots_three_vertical"
            android:contentDescription="@string/content_description_show_popup_menu"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:tint="?attr/color_main2_500"/>

        <View
            android:id="@+id/background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/color_main2_000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/back" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/participants_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"/>


        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/add_participants"
            android:onClick="@{addParticipantsClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="16dp"
            android:src="@drawable/plus_circle"
            android:contentDescription="@string/content_description_add_participants"
            android:visibility="@{viewModel.conferenceModel.isMeAdmin ? View.VISIBLE : View.GONE, default=gone}"
            app:tint="?attr/color_on_main"
            app:backgroundTint="?attr/color_main1_500"
            app:shapeAppearanceOverlay="@style/rounded"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>