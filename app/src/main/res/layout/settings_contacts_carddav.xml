<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <import type="android.text.InputType" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.CardDavViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_main2_000">

        <ImageView
            android:id="@+id/back"
            android:onClick="@{backClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:text="@{viewModel.isEdit ? @string/settings_contacts_edit_carddav_server_title : @string/settings_contacts_add_carddav_server_title, default=@string/settings_contacts_add_carddav_server_title}"
            app:layout_constraintEnd_toStartOf="@id/delete"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/delete"
            android:onClick="@{() -> viewModel.delete()}"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/trash_simple"
            android:contentDescription="@string/content_description_carddav_delete"
            android:visibility="@{viewModel.isEdit ? View.VISIBLE : View.GONE, default=gone}"
            app:tint="?attr/color_main1_500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="@id/back"
            app:layout_constraintBottom_toBottomOf="@id/back"/>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:background="?attr/color_grey_100"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/name_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_carddav_name_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/name"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.displayName}"
                    android:inputType="text|textPersonName"
                    android:hint="@string/settings_contacts_carddav_name_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/name_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/server_url_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_carddav_server_url_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/server_url"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.serverUrl}"
                    android:inputType="text|textUri"
                    android:hint="@string/settings_contacts_carddav_server_url_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/server_url_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/username_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_carddav_username_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/server_url"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/username"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.username}"
                    android:inputType="text"
                    android:hint="@string/username"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/username_title"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/password_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_carddav_password_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/username"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/password"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.password}"
                    android:hint="@string/password"
                    passwordInputType="@{viewModel.showPassword ? InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD : InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD, default=textPassword}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/password_title" />

                <ImageView
                    android:onClick="@{() -> viewModel.toggleShowPassword()}"
                    android:id="@+id/eye"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="0dp"
                    android:padding="4dp"
                    android:layout_marginEnd="20dp"
                    android:contentDescription="@string/content_description_toggle_password_visibility"
                    android:src="@{viewModel.showPassword ? @drawable/eye_slash : @drawable/eye, default=@drawable/eye}"
                    app:tint="?attr/color_main2_500"
                    app:layout_constraintEnd_toEndOf="@id/password"
                    app:layout_constraintTop_toTopOf="@id/password"
                    app:layout_constraintBottom_toBottomOf="@id/password" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/realm_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="@string/settings_contacts_carddav_realm_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toBottomOf="@id/password"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <androidx.appcompat.widget.AppCompatEditText
                    style="@style/default_text_style"
                    android:id="@+id/realm"
                    android:layout_width="0dp"
                    android:layout_height="50dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginBottom="@dimen/screen_bottom_margin"
                    android:background="@drawable/edit_text_background"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:text="@={viewModel.realm}"
                    android:inputType="text"
                    android:hint="@string/settings_contacts_carddav_realm_title"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/realm_title"
                    app:layout_constraintBottom_toBottomOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/settings_title_style"
                    android:id="@+id/save_new_contacts_in_this_list_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="10dp"
                    android:text="@string/settings_contacts_carddav_use_as_default_title"
                    android:maxLines="2"
                    android:ellipsize="end"
                    app:layout_constraintTop_toTopOf="@id/save_new_contacts_in_this_list_switch"
                    app:layout_constraintBottom_toBottomOf="@id/save_new_contacts_in_this_list_switch"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/save_new_contacts_in_this_list_switch"/>

                <com.google.android.material.materialswitch.MaterialSwitch
                    style="@style/material_switch_style"
                    android:id="@+id/save_new_contacts_in_this_list_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:checked="@={viewModel.storeNewContactsInIt}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/realm" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/add_account"
            android:onClick="@{() -> viewModel.addAddressBook()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="16dp"
            android:contentDescription="@string/content_description_carddav_save"
            android:src="@{viewModel.isEdit ? @drawable/check : @drawable/plus_circle, default=@drawable/plus_circle}"
            android:visibility="@{viewModel.syncInProgress ? View.GONE : View.VISIBLE}"
            app:tint="?attr/color_on_main"
            app:backgroundTint="?attr/color_main1_500"
            app:shapeAppearanceOverlay="@style/rounded"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <include
            layout="@layout/operation_in_progress"
            bind:visibility="@{viewModel.syncInProgress}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>