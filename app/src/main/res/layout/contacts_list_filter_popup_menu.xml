<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="noFilterClickListener"
            type="View.OnClickListener" />
        <variable
            name="linphoneOnlyClickListener"
            type="View.OnClickListener" />
        <variable
            name="sipOnlyClickListener"
            type="View.OnClickListener" />
        <variable
            name="seeAllSelected"
            type="Boolean" />
        <variable
            name="showLinphoneFilter"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/popup_menu_padding"
        android:paddingStart="@dimen/popup_menu_padding"
        android:background="@drawable/shape_round_popup_menu_background">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/no_filter"
            android:onClick="@{noFilterClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/popup_menu_item_top_margin"
            android:text="@string/contacts_list_filter_popup_see_all"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:drawablePadding="5dp"
            android:drawableEnd="@drawable/check"
            android:drawableTint="@{seeAllSelected ? @color/green_success_500 : @color/transparent_color, default=@color/transparent_color}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/linphone_filter"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/linphone_filter"
            android:onClick="@{linphoneOnlyClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/popup_menu_item_top_margin"
            android:text="@string/contacts_list_filter_popup_see_linphone_only"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:visibility="@{showLinphoneFilter ? View.VISIBLE : View.GONE}"
            android:drawablePadding="5dp"
            android:drawableEnd="@drawable/check"
            android:drawableTint="@{seeAllSelected ? @color/transparent_color : @color/green_success_500, default=@color/green_success_500}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/no_filter"
            app:layout_constraintBottom_toTopOf="@id/sip_filter"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/sip_filter"
            android:onClick="@{sipOnlyClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/popup_menu_item_top_margin"
            android:text="@string/contacts_list_filter_popup_see_sip_only"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:visibility="@{showLinphoneFilter ? View.GONE : View.VISIBLE}"
            android:drawablePadding="5dp"
            android:drawableEnd="@drawable/check"
            android:drawableTint="@{seeAllSelected ? @color/transparent_color : @color/green_success_500, default=@color/green_success_500}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/linphone_filter"
            app:layout_constraintBottom_toTopOf="@id/bottom_anchor"/>

        <View
            android:id="@+id/bottom_anchor"
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_marginBottom="@dimen/popup_menu_item_top_margin"
            app:layout_constraintWidth_max="@dimen/popup_menu_width"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sip_filter"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>