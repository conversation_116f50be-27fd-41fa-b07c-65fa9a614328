<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.recordings.viewmodel.RecordingsListViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.recordings.empty ? View.VISIBLE : View.GONE}"
            app:constraint_referenced_ids="no_recording_image, no_recording_label" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.searchBarVisible ? View.VISIBLE : View.GONE, default=gone}"
            app:constraint_referenced_ids="cancel_search, search, clear_field" />

        <ImageView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:contentDescription="@string/content_description_go_back_icon"
            android:onClick="@{backClickListener}"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            app:tint="?attr/color_main1_500" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            style="@style/main_page_title_style"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@string/recordings_title"
            android:visibility="@{viewModel.searchBarVisible ? View.GONE : View.VISIBLE}"
            app:layout_constraintEnd_toStartOf="@id/search_toggle"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/search_toggle"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:contentDescription="@string/content_description_open_filter"
            android:onClick="@{() -> viewModel.openSearchBar()}"
            android:padding="15dp"
            android:src="@drawable/magnifying_glass"
            android:visibility="@{viewModel.searchBarVisible ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            app:tint="?attr/color_main2_500" />

        <ImageView
            android:id="@+id/cancel_search"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:contentDescription="@string/content_description_cancel_filter"
            android:onClick="@{() -> viewModel.closeSearchBar()}"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            app:layout_constraintBottom_toBottomOf="@id/search"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/search"
            app:tint="?attr/color_main2_500" />

        <com.google.android.material.textfield.TextInputLayout
            style="?attr/textInputFilledStyle"
            android:id="@+id/search"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            android:gravity="center_vertical"
            android:textColorHint="?attr/color_main2_400"
            app:boxStrokeWidth="0dp"
            app:boxStrokeWidthFocused="0dp"
            app:hintAnimationEnabled="false"
            app:hintEnabled="false"
            app:hintTextColor="?attr/color_main2_400"
            app:layout_constraintEnd_toStartOf="@id/clear_field"
            app:layout_constraintStart_toEndOf="@id/cancel_search"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:inputType="text"
                android:paddingVertical="1dp"
                android:text="@={viewModel.searchFilter}"
                android:textCursorDrawable="@null"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <ImageView
            android:id="@+id/clear_field"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="9dp"
            android:contentDescription="@string/content_description_clear_filter"
            android:enabled="@{viewModel.searchFilter.length() > 0}"
            android:onClick="@{() -> viewModel.clearFilter()}"
            android:padding="6dp"
            android:src="@drawable/x"
            app:layout_constraintBottom_toBottomOf="@id/search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/search"
            app:tint="?attr/color_main2_500" />

        <View
            android:id="@+id/background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/top_bar_height"
            android:background="?attr/color_main2_000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/no_recording_image"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:contentDescription="@null"
            android:src="@drawable/illu_recordings"
            app:layout_constraintBottom_toTopOf="@id/no_recording_label"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="200dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintVertical_chainStyle="packed" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/no_recording_label"
            style="@style/section_header_style"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/recordings_list_empty"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/no_recording_image" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recordings_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/top_bar_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/fetch_in_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="@{viewModel.fetchInProgress ? View.VISIBLE : View.GONE}"
            app:indicatorColor="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>