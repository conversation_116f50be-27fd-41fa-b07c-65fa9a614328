<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="android.text.InputType" />
        <variable
            name="viewModel"
            type="org.linphone.utils.PasswordDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{() -> viewModel.dismiss()}"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/dialog_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="2dp"
            android:src="@drawable/shape_dialog_background"
            android:contentDescription="@null"
            app:layout_constraintWidth_max="@dimen/dialog_max_width"
            app:layout_constraintBottom_toBottomOf="@id/anchor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/section_header_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingTop="@dimen/dialog_top_bottom_margin"
            android:text="@string/account_settings_update_password_title"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/password"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/default_text_style"
            android:id="@+id/password"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="16dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:hint="@string/account_settings_dialog_invalid_password_hint"
            android:text="@={viewModel.password}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_600"
            android:maxLines="1"
            android:background="@drawable/edit_text_background"
            passwordInputType="@{viewModel.showPassword ? InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD : InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD, default=textPassword}"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintWidth_max="@dimen/text_input_max_width"
            app:layout_constraintBottom_toTopOf="@id/cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/title"/>

        <ImageView
            android:id="@+id/eye"
            android:onClick="@{() -> viewModel.toggleShowPassword()}"
            android:layout_width="@dimen/icon_size"
            android:layout_height="0dp"
            android:padding="4dp"
            android:layout_marginEnd="20dp"
            android:contentDescription="@string/content_description_toggle_password_visibility"
            android:src="@{viewModel.showPassword ? @drawable/eye_slash : @drawable/eye, default=@drawable/eye}"
            app:tint="?attr/color_main2_500"
            app:layout_constraintEnd_toEndOf="@id/password"
            app:layout_constraintTop_toTopOf="@id/password"
            app:layout_constraintBottom_toBottomOf="@id/password" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.dismiss()}"
            style="@style/secondary_button_label_style"
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/dialog_cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/password"
            app:layout_constraintBottom_toTopOf="@id/confirm"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.confirm()}"
            style="@style/primary_button_label_style"
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/dialog_confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/cancel"
            app:layout_constraintBottom_toTopOf="@id/anchor"/>

        <View
            android:id="@+id/anchor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dialog_top_bottom_margin"
            app:layout_constraintTop_toBottomOf="@id/confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>