<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.call.conference.model.ConferenceParticipantDeviceModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="@dimen/call_conference_audio_only_miniature_height"
        android:padding="8dp"
        app:layout_columnWeight="1"
        app:layout_gravity="fill_horizontal">

        <ImageView
            android:id="@+id/participant_device_background"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:background="@drawable/shape_round_in_call_cell_gray_background"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            layout="@layout/contact_avatar"
            bind:model="@{model.avatarModel}"
            bind:hidePresence="@{true}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/muted"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginEnd="10dp"
            android:padding="2dp"
            android:src="@drawable/microphone_slash"
            android:contentDescription="@string/content_description_conference_participant_muted"
            android:background="@drawable/shape_circle_white_call_background"
            android:visibility="@{model.isMuted ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/joining"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/joining"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginEnd="10dp"
            android:indeterminate="true"
            android:visibility="@{model.isJoining ? View.VISIBLE : View.GONE, default=gone}"
            app:indicatorColor="@color/bc_white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/paused"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/paused"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginEnd="10dp"
            android:contentDescription="@null"
            android:src="@drawable/pause"
            android:visibility="@{!model.isInConference &amp;&amp; !model.isJoining ? View.VISIBLE : View.GONE, default=gone}"
            app:tint="@color/bc_white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_500"
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:text="@{model.name, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintEnd_toStartOf="@id/muted"/>

        <ImageView
            android:id="@+id/speaking"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/shape_squircle_main2_200_call_border"
            android:contentDescription="@string/content_description_conference_participant_speaking"
            android:visibility="@{model.isSpeaking ? View.VISIBLE : View.GONE}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>