<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="digit"
            type="String" />
        <variable
            name="letters"
            type="String" />
        <variable
            name="model"
            type="org.linphone.ui.main.history.model.NumpadModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{() -> model.onDigitClicked(digit)}"
        android:onLongClick="@{() -> model.onDigitLongClicked(digit)}"
        android:layout_width="@dimen/call_dtmf_button_size"
        android:layout_height="@dimen/call_dtmf_button_size">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:src="@drawable/in_call_button_background_red"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/digit_label"
            android:text="@{digit, default=`9`}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="25sp"
            android:includeFontPadding="false"
            android:textColor="@color/bc_white"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/letters_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/letters_label"
            android:text="@{letters, default=`wxyz`}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:includeFontPadding="false"
            android:textColor="@color/bc_white"
            android:visibility="@{model.showLetters ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toBottomOf="@id/digit_label"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>