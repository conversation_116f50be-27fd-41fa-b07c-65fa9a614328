<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.main.meetings.model.MeetingListItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/week_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="5dp"
            android:paddingTop="5dp"
            android:text="@{model.weekLabel, default=`22 - 28 Avril`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:maxLines="1"
            android:ellipsize="end"
            android:visibility="@{model.firstMeetingOfTheWeek ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/header_day"
            app:layout_constraintEnd_toEndOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/header_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text="@{model.day, default=`Mon.`}"
            android:textColor="?attr/color_main2_500"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@id/header_day_number"
            app:layout_constraintEnd_toEndOf="@id/header_day_number"
            app:layout_constraintTop_toBottomOf="@id/week_label"/>

        <ImageView
            android:id="@+id/today_background"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="5dp"
            android:contentDescription="@null"
            android:src="@drawable/shape_circle_primary_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/header_day" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_800"
            android:id="@+id/header_day_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.dayNumber, default=`19`}"
            android:textColor="?attr/color_on_main"
            android:textSize="20sp"
            android:paddingBottom="4dp"
            app:layout_constraintStart_toStartOf="@id/today_background"
            app:layout_constraintEnd_toEndOf="@id/today_background"
            app:layout_constraintBottom_toBottomOf="@id/today_background"
            app:layout_constraintTop_toBottomOf="@id/header_day"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_700"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="5dp"
            android:paddingStart="16dp"
            android:text="@string/meetings_list_no_meeting_for_today"
            android:textSize="13sp"
            android:textColor="?attr/color_main2_600"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/header_day"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/header_day_number"
            app:layout_constraintBottom_toBottomOf="@id/header_day_number"
            tools:ignore="RtlSymmetry" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>