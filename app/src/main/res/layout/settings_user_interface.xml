<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:background="@drawable/shape_squircle_white_background">

        <androidx.constraintlayout.widget.Group
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.showThemeSelector ? View.VISIBLE : View.GONE}"
            app:constraint_referenced_ids="theme_spinner, theme_spinner_caret, theme_title" />

        <androidx.constraintlayout.widget.Group
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.showColorSelector ? View.VISIBLE : View.GONE}"
            app:constraint_referenced_ids="color_spinner, color_spinner_caret, color_title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:id="@+id/auto_show_dialpad_title"
            android:onClick="@{() -> viewModel.toggleAutoShowDialpad()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_user_interface_auto_show_dialpad_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/auto_show_dialpad_switch"
            app:layout_constraintBottom_toBottomOf="@id/auto_show_dialpad_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/auto_show_dialpad_switch"/>

        <com.google.android.material.materialswitch.MaterialSwitch
            style="@style/material_switch_style"
            android:id="@+id/auto_show_dialpad_switch"
            android:onClick="@{() -> viewModel.toggleAutoShowDialpad()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="16dp"
            android:checked="@{viewModel.autoShowDialpad}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:id="@+id/theme_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_user_interface_theme_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/auto_show_dialpad_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatSpinner
            style="@style/material_switch_style"
            android:id="@+id/theme_spinner"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:paddingStart="@dimen/spinner_start_padding"
            android:paddingEnd="@dimen/spinner_end_padding"
            android:overlapAnchor="false"
            android:spinnerMode="dropdown"
            android:popupBackground="@drawable/shape_squircle_white_background"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintWidth_max="@dimen/text_input_max_width"
            app:layout_constraintTop_toBottomOf="@id/theme_title"
            app:layout_constraintStart_toStartOf="@id/theme_title"
            app:layout_constraintEnd_toEndOf="@id/theme_title" />

        <ImageView
            android:id="@+id/theme_spinner_caret"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/spinner_caret_end_margin"
            android:src="@drawable/caret_down"
            android:contentDescription="@string/content_description_spinner_caret"
            app:tint="?attr/color_main2_600"
            app:layout_constraintTop_toTopOf="@id/theme_spinner"
            app:layout_constraintBottom_toBottomOf="@id/theme_spinner"
            app:layout_constraintEnd_toEndOf="@id/theme_spinner"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:id="@+id/color_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_user_interface_color_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/theme_spinner"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatSpinner
            style="@style/material_switch_style"
            android:id="@+id/color_spinner"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:paddingStart="@dimen/spinner_start_padding"
            android:paddingEnd="@dimen/spinner_end_padding"
            android:overlapAnchor="false"
            android:spinnerMode="dropdown"
            android:popupBackground="@drawable/shape_squircle_white_background"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintWidth_max="@dimen/text_input_max_width"
            app:layout_constraintTop_toBottomOf="@id/color_title"
            app:layout_constraintStart_toStartOf="@id/color_title"
            app:layout_constraintEnd_toEndOf="@id/color_title" />

        <ImageView
            android:id="@+id/color_spinner_caret"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/spinner_caret_end_margin"
            android:src="@drawable/caret_down"
            android:contentDescription="@string/content_description_spinner_caret"
            app:tint="?attr/color_main2_600"
            app:layout_constraintTop_toTopOf="@id/color_spinner"
            app:layout_constraintBottom_toBottomOf="@id/color_spinner"
            app:layout_constraintEnd_toEndOf="@id/color_spinner"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>