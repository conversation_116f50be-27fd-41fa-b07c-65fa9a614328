<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:background="@drawable/shape_squircle_white_background">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:id="@+id/layout_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_meetings_default_layout_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatSpinner
            style="@style/material_switch_style"
            android:id="@+id/layout_spinner"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/edit_text_background"
            android:paddingStart="@dimen/spinner_start_padding"
            android:paddingEnd="@dimen/spinner_end_padding"
            android:overlapAnchor="false"
            android:spinnerMode="dropdown"
            android:popupBackground="@drawable/shape_squircle_white_background"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintWidth_max="@dimen/text_input_max_width"
            app:layout_constraintTop_toBottomOf="@id/layout_title"
            app:layout_constraintStart_toStartOf="@id/layout_title"
            app:layout_constraintEnd_toEndOf="@id/layout_title" />

        <ImageView
            android:id="@+id/layout_spinner_caret"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/spinner_caret_end_margin"
            android:src="@drawable/caret_down"
            android:contentDescription="@string/content_description_spinner_caret"
            app:tint="?attr/color_main2_600"
            app:layout_constraintTop_toTopOf="@id/layout_spinner"
            app:layout_constraintBottom_toBottomOf="@id/layout_spinner"
            app:layout_constraintEnd_toEndOf="@id/layout_spinner"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>