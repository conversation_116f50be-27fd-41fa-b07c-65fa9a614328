<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
        <variable
            name="conferenceViewModel"
            type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_900">

        <org.linphone.ui.call.conference.view.GridBoxLayout
            android:id="@+id/grid_box_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            entries="@{conferenceViewModel.participantDevices}"
            layout="@{@layout/call_conference_grid_cell}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>