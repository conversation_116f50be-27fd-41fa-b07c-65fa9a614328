<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="firstContactStartingByThatLetter"
            type="Boolean" />
        <variable
            name="model"
            type="org.linphone.ui.main.contacts.model.ContactAvatarModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{onClickListener}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp">

        <ImageView
            android:id="@+id/background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="5dp"
            android:src="@drawable/primary_cell_background"
            android:contentDescription="@null"
            app:layout_constraintStart_toEndOf="@id/header"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_500"
            android:id="@+id/header"
            android:layout_width="25dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@{model.firstLetter, default=`A`}"
            android:visibility="@{firstContactStartingByThatLetter ? View.VISIBLE : View.INVISIBLE}"
            android:textColor="?attr/color_main2_400"
            android:textSize="20sp"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            layout="@layout/contact_avatar"
            bind:model="@{model}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/header"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{model.displayName, default=`John Doe`}"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="?attr/color_main2_800"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintEnd_toStartOf="@id/selected"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/selected"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:contentDescription="@null"
            android:src="@drawable/check"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?attr/color_main1_500" />

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>