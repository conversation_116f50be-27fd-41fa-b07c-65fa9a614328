<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="turnOnVfsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.settings.viewmodel.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:background="@drawable/shape_squircle_white_background">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:onClick="@{turnOnVfsClickListener}"
            android:id="@+id/enable_vfs_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:enabled="@{!viewModel.isVfsEnabled}"
            android:text="@string/settings_security_enable_vfs_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/enable_vfs_switch"
            app:layout_constraintBottom_toTopOf="@id/enable_vfs_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/enable_vfs_switch"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_subtitle_style"
            android:id="@+id/enable_vfs_subtitle"
            android:onClick="@{turnOnVfsClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:enabled="@{!viewModel.isVfsEnabled}"
            android:text="@string/settings_security_enable_vfs_subtitle"
            app:layout_constraintTop_toBottomOf="@id/enable_vfs_title"
            app:layout_constraintBottom_toBottomOf="@id/enable_vfs_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/enable_vfs_switch"/>

        <com.google.android.material.materialswitch.MaterialSwitch
            style="@style/material_switch_style"
            android:id="@+id/enable_vfs_switch"
            android:onClick="@{turnOnVfsClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="16dp"
            android:enabled="@{!viewModel.isVfsEnabled}"
            android:checked="@{viewModel.isVfsEnabled}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.materialswitch.MaterialSwitch
            style="@style/material_switch_style"
            android:id="@+id/security_ui_switch"
            android:onClick="@{() -> viewModel.toggleUiSecureMode()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="16dp"
            android:checked="@{viewModel.isUiSecureModeEnabled}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/enable_vfs_subtitle" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/settings_title_style"
            android:id="@+id/security_ui_title"
            android:onClick="@{() -> viewModel.toggleUiSecureMode()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:text="@string/settings_security_prevent_screenshots_title"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@id/security_ui_switch"
            app:layout_constraintBottom_toBottomOf="@id/security_ui_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/security_ui_switch"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>