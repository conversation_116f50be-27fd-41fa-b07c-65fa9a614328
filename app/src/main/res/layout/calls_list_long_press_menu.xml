<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <import type="android.view.View" />
        <variable
            name="pauseResumeClickListener"
            type="View.OnClickListener" />
        <variable
            name="hangUpClickListener"
            type="View.OnClickListener" />
        <variable
            name="isPaused"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_separator">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/pause"
            android:onClick="@{pauseResumeClickListener}"
            android:visibility="@{isPaused ? View.GONE : View.VISIBLE}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/call_action_pause_call"
            style="@style/context_menu_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/phone_pause"
            app:layout_constraintBottom_toTopOf="@id/hang_up"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/resume"
            android:onClick="@{pauseResumeClickListener}"
            android:visibility="@{isPaused ? View.VISIBLE : View.GONE, default=gone}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/call_action_resume_call"
            style="@style/context_menu_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/phone_pause"
            app:layout_constraintBottom_toTopOf="@id/hang_up"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/hang_up"
            android:onClick="@{hangUpClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/call_action_hang_up"
            style="@style/context_menu_danger_action_label_style"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/phone_disconnect"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>