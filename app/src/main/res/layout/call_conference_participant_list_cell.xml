<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="onLongClickListener"
            type="View.OnLongClickListener" />
        <variable
            name="model"
            type="org.linphone.ui.call.conference.model.ConferenceParticipantModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{onClickListener}"
        android:onLongClick="@{onLongClickListener}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/primary_cell_background">

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            layout="@layout/contact_avatar"
            bind:model="@{model.avatarModel}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{model.avatarModel.name, default=`John Doe`}"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintEnd_toStartOf="@id/admin"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/admin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/conversation_info_participant_is_admin_label"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_400"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            android:visibility="@{model.isAdmin &amp;&amp; !model.isMeAdmin ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toEndOf="@id/name"
            app:layout_constraintEnd_toStartOf="@id/admin_toggle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.google.android.material.materialswitch.MaterialSwitch
            style="@style/material_switch_style"
            android:id="@+id/admin_toggle"
            android:onClick="@{() -> model.toggleAdminStatus()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@string/conversation_info_participant_is_admin_label"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_400"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            android:enabled="@{!model.isMyself}"
            android:checked="@{model.isAdmin}"
            android:visibility="@{model.isMeAdmin ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintStart_toEndOf="@id/admin"
            app:layout_constraintEnd_toStartOf="@id/remove_participant"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/remove_participant"
            android:onClick="@{() -> model.removeParticipant()}"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginStart="10dp"
            android:src="@drawable/x"
            android:contentDescription="@string/content_description_click_to_remove_participant"
            android:visibility="@{model.isMeAdmin ? (model.isMyself ? View.INVISIBLE : View.VISIBLE) : View.GONE, default=gone}"
            app:layout_constraintStart_toEndOf="@id/admin_toggle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="?attr/color_danger_500" />

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>