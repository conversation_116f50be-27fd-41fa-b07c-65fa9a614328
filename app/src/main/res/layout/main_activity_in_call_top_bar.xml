<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.viewmodel.MainViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/color_success_500"
        android:onClick="@{() -> viewModel.onCallTopBarClicked()}">

        <ImageView
            android:id="@+id/icon"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginStart="16dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:src="@drawable/phone"
            android:contentDescription="@null"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:tint="@color/bc_white" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/label"
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:gravity="center_vertical"
            android:text="@{viewModel.callLabel, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toStartOf="@id/call_time"
            app:layout_constraintStart_toEndOf="@id/icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/call_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:gravity="center_vertical"
            android:text="@{viewModel.callsStatus, default=`Paused`}"
            android:textColor="@color/bc_white"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/label"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>