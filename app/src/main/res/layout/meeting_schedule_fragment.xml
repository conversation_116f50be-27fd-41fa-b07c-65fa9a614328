<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="pickStartDateClickListener"
            type="View.OnClickListener" />
        <variable
            name="pickStartTimeClickListener"
            type="View.OnClickListener" />
        <variable
            name="pickEndTimeClickListener"
            type="View.OnClickListener" />
        <variable
            name="pickParticipantsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.meetings.viewmodel.ScheduleMeetingViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_background_contrast_in_dark_mode">

            <ImageView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="15dp"
                android:adjustViewBounds="true"
                android:onClick="@{backClickListener}"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:tint="?attr/color_main1_500"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/title"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/main_page_title_style"
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="@dimen/top_bar_height"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:text="@string/meeting_schedule_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="parent"/>

            <ScrollView
                android:id="@+id/scrollView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fillViewport="true"
                android:background="?attr/color_main2_000"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/meeting_selected"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_meeting_label"
                        android:textColor="@color/primary_button_label_color"
                        android:textSize="16sp"
                        android:gravity="center"
                        android:drawableStart="@drawable/users_three"
                        android:drawableTint="@color/primary_button_label_color"
                        android:drawablePadding="5dp"
                        android:background="@drawable/primary_button_background"
                        android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:visibility="@{viewModel.hideBroadcast || viewModel.isBroadcastSelected ? View.GONE : View.VISIBLE, default=gone}"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/broadcast"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/broadcast"
                        android:onClick="@{() -> viewModel.selectBroadcast()}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_broadcast_label"
                        android:textColor="@color/secondary_button_label_color"
                        android:textSize="16sp"
                        android:gravity="center"
                        android:drawableStart="@drawable/slideshow"
                        android:drawableTint="@color/secondary_button_label_color"
                        android:drawablePadding="5dp"
                        android:background="@drawable/secondary_button_background"
                        android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:visibility="@{viewModel.hideBroadcast || viewModel.isBroadcastSelected ? View.GONE : View.VISIBLE, default=gone}"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toEndOf="@id/meeting_selected"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/meeting"
                        android:onClick="@{() -> viewModel.selectMeeting()}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_meeting_label"
                        android:textColor="@color/secondary_button_label_color"
                        android:textSize="16sp"
                        android:gravity="center"
                        android:drawableStart="@drawable/users_three"
                        android:drawableTint="@color/secondary_button_label_color"
                        android:drawablePadding="5dp"
                        android:background="@drawable/secondary_button_background"
                        android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:visibility="@{viewModel.isBroadcastSelected ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/meeting_selected"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/broadcast_selected"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/broadcast_selected"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_broadcast_label"
                        android:textColor="@color/primary_button_label_color"
                        android:textSize="16sp"
                        android:gravity="center"
                        android:drawableStart="@drawable/slideshow"
                        android:drawableTint="@color/primary_button_label_color"
                        android:drawablePadding="5dp"
                        android:background="@drawable/primary_button_background"
                        android:paddingTop="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingBottom="@dimen/primary_secondary_buttons_label_padding"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:visibility="@{viewModel.isBroadcastSelected ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/broadcast"
                        app:layout_constraintStart_toEndOf="@id/meeting"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/broadcast_help"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:text="@string/meeting_schedule_broadcast_help"
                        android:textSize="14sp"
                        android:textColor="?attr/color_main2_500"
                        android:drawableStart="@drawable/info"
                        android:drawablePadding="5dp"
                        android:gravity="center"
                        android:background="@drawable/shape_squircle_main2_100_background"
                        android:visibility="@{viewModel.showBroadcastHelp ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/meeting"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <ImageView
                        android:id="@+id/close_broadcast_help"
                        android:onClick="@{() -> viewModel.closeBroadcastHelp()}"
                        android:layout_width="@dimen/icon_size"
                        android:layout_height="@dimen/icon_size"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/x"
                        android:contentDescription="@null"
                        android:visibility="@{viewModel.showBroadcastHelp ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toTopOf="@id/broadcast_help"
                        app:layout_constraintBottom_toBottomOf="@id/broadcast_help"
                        app:layout_constraintEnd_toEndOf="@id/broadcast_help" />

                    <androidx.appcompat.widget.AppCompatEditText
                        style="@style/default_text_style_800"
                        android:id="@+id/subject"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:hint="@string/meeting_schedule_subject_hint"
                        android:textColorHint="?attr/color_main2_600"
                        android:text="@={viewModel.subject}"
                        android:textSize="20sp"
                        android:textColor="?attr/color_main2_600"
                        android:inputType="text|textCapSentences"
                        android:drawableStart="@{viewModel.isBroadcastSelected ? @drawable/slideshow : @drawable/users_three, default=@drawable/users_three}"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        android:background="@color/transparent_color"
                        app:layout_constraintTop_toBottomOf="@id/broadcast_help"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <View
                        android:id="@+id/separator"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="10dp"
                        android:background="?attr/color_separator"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/subject" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/from_date"
                        android:onClick="@{pickStartDateClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:text="@{viewModel.fromDate, default=`Tue. October 10th, 2023`}"
                        android:textSize="14sp"
                        android:textColor="?attr/color_main2_600"
                        android:drawableStart="@drawable/clock"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/separator" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/from_time"
                        android:onClick="@{pickStartTimeClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="32dp"
                        android:layout_marginTop="10dp"
                        android:text="@{viewModel.fromTime, default=`17:00`}"
                        android:textSize="14sp"
                        android:textColor="?attr/color_main2_600"
                        app:layout_constraintStart_toStartOf="@id/from_date"
                        app:layout_constraintTop_toBottomOf="@id/from_date" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/to_time"
                        android:onClick="@{pickEndTimeClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="45dp"
                        android:text="@{viewModel.toTime, default=`18:00`}"
                        android:textSize="14sp"
                        android:textColor="?attr/color_main2_600"
                        app:layout_constraintStart_toEndOf="@id/from_time"
                        app:layout_constraintTop_toTopOf="@id/from_time" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/timezone_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_timezone_title"
                        android:textColor="?attr/color_main2_600"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:drawableStart="@drawable/globe_hemisphere_west"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/to_time" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        style="@style/default_text_style"
                        android:id="@+id/timezone_picker"
                        android:layout_width="0dp"
                        android:layout_height="50dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="8dp"
                        android:paddingStart="20dp"
                        android:paddingEnd="20dp"
                        android:textSize="14sp"
                        android:textColor="@color/gray_main2_600"
                        android:gravity="center_vertical"
                        android:overlapAnchor="false"
                        android:dropDownVerticalOffset="25dp"
                        android:spinnerMode="dropdown"
                        android:popupBackground="@drawable/shape_squircle_white_background"
                        android:background="@drawable/edit_text_background"
                        app:layout_constraintTop_toBottomOf="@id/timezone_label"
                        app:layout_constraintStart_toStartOf="@id/timezone_label"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <ImageView
                        android:id="@+id/timezone_picker_caret"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/caret_down"
                        android:contentDescription="@null"
                        app:tint="?attr/color_main2_600"
                        app:layout_constraintTop_toTopOf="@id/timezone_picker"
                        app:layout_constraintBottom_toBottomOf="@id/timezone_picker"
                        app:layout_constraintEnd_toEndOf="@id/timezone_picker"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/repeat"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_one_time_label"
                        android:textColor="?attr/color_main2_600"
                        android:textSize="14sp"
                        android:drawableStart="@drawable/arrow_clockwise"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/timezone_picker" />

                    <View
                        android:id="@+id/separator_2"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:background="?attr/color_separator"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/repeat" />

                    <androidx.appcompat.widget.AppCompatEditText
                        style="@style/default_text_style_700"
                        android:id="@+id/description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:hint="@string/meeting_schedule_description_hint"
                        android:textColorHint="?attr/color_main2_600"
                        android:text="@={viewModel.description}"
                        android:textSize="14sp"
                        android:textColor="?attr/color_main2_600"
                        android:inputType="text|textCapSentences"
                        android:drawableStart="@drawable/file_text"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        android:background="@color/transparent_color"
                        app:layout_constraintTop_toBottomOf="@id/separator_2"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

                    <View
                        android:id="@+id/separator_3"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:background="?attr/color_separator"
                        android:visibility="@{viewModel.isBroadcastSelected ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/description" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/speakers"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_add_speaker_title"
                        android:textColor="?attr/color_main2_600"
                        android:textSize="14sp"
                        android:drawableStart="@drawable/user_square"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        android:visibility="@{viewModel.isBroadcastSelected ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/separator_3" />

                    <View
                        android:id="@+id/separator_4"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:background="?attr/color_separator"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/speakers" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/participants"
                        android:onClick="@{pickParticipantsClickListener}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:text="@string/meeting_schedule_add_participants_title"
                        android:textColor="?attr/color_main2_600"
                        android:textSize="14sp"
                        android:drawableStart="@drawable/users"
                        android:drawablePadding="8dp"
                        android:drawableTint="?attr/color_main2_600"
                        android:visibility="@{viewModel.participants.size() > 0 ? View.GONE : View.VISIBLE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/separator_4" />

                    <ImageView
                        android:id="@+id/participants_list_icon"
                        android:onClick="@{pickParticipantsClickListener}"
                        android:layout_width="@dimen/icon_size"
                        android:layout_height="@dimen/icon_size"
                        android:layout_marginStart="16dp"
                        android:src="@drawable/users"
                        android:contentDescription="@string/content_description_meeting_participants_list"
                        android:visibility="@{viewModel.participants.size() > 0 ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/add_more_participants" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_300"
                        android:id="@+id/add_more_participants"
                        android:onClick="@{pickParticipantsClickListener}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:text="@string/meeting_schedule_add_more_participants_title"
                        android:visibility="@{viewModel.participants.size() > 0 ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/participants"
                        app:layout_constraintStart_toEndOf="@id/participants_list_icon" />

                    <LinearLayout
                        android:id="@+id/participants_list"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="10dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="16dp"
                        android:visibility="@{viewModel.participants.size() > 0 ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintTop_toBottomOf="@id/add_more_participants"
                        app:layout_constraintStart_toEndOf="@id/participants_list_icon"
                        app:layout_constraintEnd_toEndOf="parent"
                        entries="@{viewModel.participants}"
                        layout="@{@layout/meeting_schedule_participant_list_cell}"/>

                    <View
                        android:id="@+id/separator_5"
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:background="?attr/color_separator"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/participants_list" />

                    <com.google.android.material.materialswitch.MaterialSwitch
                        style="@style/material_switch_style"
                        android:id="@+id/send_invitations"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:textColor="@color/gray_main2_600"
                        android:textSize="14sp"
                        android:checked="@={viewModel.sendInvitations}"
                        app:layout_constraintTop_toBottomOf="@id/separator_5"
                        app:layout_constraintStart_toStartOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/material_switch_style"
                        android:id="@+id/send_invitations_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:text="@string/meeting_schedule_send_invitations_title"
                        app:layout_constraintStart_toEndOf="@id/send_invitations"
                        app:layout_constraintTop_toTopOf="@id/send_invitations"
                        app:layout_constraintBottom_toBottomOf="@id/send_invitations" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/schedule"
            android:onClick="@{() -> viewModel.schedule()}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="16dp"
            android:src="@drawable/check"
            android:enabled="@{!viewModel.operationInProgress}"
            android:contentDescription="@string/content_description_meeting_schedule"
            app:tint="?attr/color_on_main"
            app:backgroundTint="?attr/color_main1_500"
            app:shapeAppearanceOverlay="@style/rounded" />

        <include
            layout="@layout/operation_in_progress"
            bind:visibility="@{viewModel.operationInProgress}" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>