<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onContactsClicked"
            type="View.OnClickListener" />
        <variable
            name="onConversationsClicked"
            type="View.OnClickListener" />
        <variable
            name="menuClickListener"
            type="View.OnClickListener" />
        <variable
            name="startCallClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.history.viewmodel.HistoryListViewModel" />
    </data>

    <androidx.slidingpanelayout.widget.SlidingPaneLayout
        android:id="@+id/sliding_pane_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="@dimen/sliding_pane_left_fragment_width"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/color_main1_500">

                <androidx.constraintlayout.widget.Group
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="no_calls_image, no_calls_label"
                    android:visibility="@{viewModel.callLogs.empty ? View.VISIBLE : View.GONE}" />

                <include
                    android:id="@+id/top_bar"
                    layout="@layout/main_activity_top_bar"
                    bind:viewModel="@{viewModel}"
                    bind:enableExtraAction="@{true}"
                    bind:extraActionIcon="@{@drawable/dots_three_vertical}"
                    bind:extraActionClickListener="@{menuClickListener}"
                    bind:extraActionContentDescription="@{@string/content_description_show_popup_menu}"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/top_bar_height"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <View
                    android:id="@+id/shadow_effect"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="@dimen/top_bar_shadow_height"
                    android:background="@drawable/shape_squircle_white_r20_top_shadow"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/history_list"
                    android:background="@drawable/shape_squircle_white_r20_top_background"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/top_bar_height"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/bottom_nav_bar" />

                <ImageView
                    android:id="@+id/no_calls_image"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:src="@drawable/illu"
                    android:contentDescription="@null"
                    app:layout_constraintHeight_max="200dp"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/history_list"
                    app:layout_constraintBottom_toTopOf="@id/no_calls_label" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/no_calls_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.isFilterEmpty ? @string/history_list_empty_history : @string/list_filter_no_result_found, default=@string/history_list_empty_history}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/no_calls_image"
                    app:layout_constraintBottom_toBottomOf="@id/history_list" />

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/fetch_in_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    android:visibility="@{viewModel.fetchInProgress ? View.VISIBLE : View.GONE}"
                    app:indicatorColor="?attr/color_main1_500"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/bottom_nav_bar" />

                <include
                    android:id="@+id/bottom_nav_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/bottom_nav_bar"
                    bind:viewModel="@{viewModel}"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:onClick="@{startCallClickListener}"
                    android:id="@+id/new_call"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|bottom"
                    android:layout_margin="16dp"
                    android:src="@drawable/phone_plus"
                    android:contentDescription="@string/content_description_call_start"
                    app:tint="?attr/color_on_main"
                    app:backgroundTint="?attr/color_main1_500"
                    app:shapeAppearanceOverlay="@style/rounded"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/bottom_nav_bar" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/history_nav_container"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="@dimen/sliding_pane_right_fragment_width"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:defaultNavHost="false"
            app:navGraph="@navigation/history_nav_graph"/>

    </androidx.slidingpanelayout.widget.SlidingPaneLayout>

</layout>