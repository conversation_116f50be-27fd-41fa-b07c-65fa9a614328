<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onNewContactClicked"
            type="View.OnClickListener" />
        <variable
            name="filterClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.contacts.viewmodel.ContactsListViewModel" />
    </data>

    <androidx.slidingpanelayout.widget.SlidingPaneLayout
        android:id="@+id/sliding_pane_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="@dimen/sliding_pane_left_fragment_width"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.Group
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="no_contacts_image, no_contacts_label"
                    android:visibility="@{viewModel.contactsList.empty ? View.VISIBLE : View.GONE}" />

                <include
                    android:id="@+id/bottom_nav_bar"
                    android:layout_width="@dimen/landscape_nav_bar_width"
                    android:layout_height="match_parent"
                    layout="@layout/bottom_nav_bar"
                    bind:viewModel="@{viewModel}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <include
                    android:id="@+id/top_bar"
                    layout="@layout/main_activity_top_bar"
                    bind:viewModel="@{viewModel}"
                    bind:enableExtraAction="@{true}"
                    bind:extraActionIcon="@{@drawable/funnel}"
                    bind:extraActionClickListener="@{filterClickListener}"
                    bind:extraActionContentDescription="@{@string/content_description_contacts_list_filters}"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/top_bar_height"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <LinearLayout
                    android:id="@+id/lists"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/top_bar_height"
                    android:orientation="vertical"
                    android:background="?attr/color_main2_000"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/favourites_label"
                        android:visibility="@{!viewModel.isListFiltered &amp;&amp; viewModel.favourites.size() > 0 ? View.VISIBLE : View.GONE}"
                        onClickListener="@{() -> viewModel.toggleFavouritesVisibility()}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="20dp"
                        android:text="@string/contacts_list_favourites_title"
                        android:drawableEnd="@{viewModel.showFavourites ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                        android:drawableTint="@color/gray_main2_600"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/favourites_contacts_list"
                        android:visibility="@{viewModel.showFavourites &amp;&amp; !viewModel.isListFiltered &amp;&amp; viewModel.favourites.size() > 0 ? View.VISIBLE : View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/all_contacts_label"
                        android:visibility="@{!viewModel.isListFiltered &amp;&amp; viewModel.favourites.size() > 0 ? View.VISIBLE : View.GONE}"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="16dp"
                        android:text="@string/contacts_list_all_contacts_title"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/contactsList"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/no_contacts_image"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:src="@drawable/illu"
                    android:contentDescription="@null"
                    app:layout_constraintHeight_max="200dp"
                    app:layout_constraintBottom_toTopOf="@id/no_contacts_label"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"
                    app:layout_constraintTop_toBottomOf="@id/lists" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/no_contacts_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.isFilterEmpty ? (viewModel.areAllContactsDisplayed ? @string/contacts_list_empty : @string/contacts_list_sip_empty) : @string/list_filter_no_result_found, default=@string/contacts_list_sip_empty}"
                    app:layout_constraintBottom_toTopOf="@id/lists"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"
                    app:layout_constraintTop_toBottomOf="@id/no_contacts_image" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/default_text_style_600"
                    android:id="@+id/show_contacts_filter"
                    android:onClick="@{filterClickListener}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:paddingTop="6dp"
                    android:paddingBottom="6dp"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:text="@string/contacts_change_filter_label"
                    android:textSize="13sp"
                    android:textColor="@color/secondary_button_label_color"
                    android:gravity="center"
                    android:background="@drawable/secondary_button_background"
                    android:visibility="@{viewModel.contactsList.empty &amp;&amp; !viewModel.areAllContactsDisplayed ? View.VISIBLE : View.GONE}"
                    app:layout_constraintTop_toBottomOf="@id/no_contacts_label"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"/>

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/fetch_in_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    android:visibility="@{viewModel.fetchInProgress || viewModel.searchInProgress ? View.VISIBLE : View.GONE}"
                    app:indicatorColor="?attr/color_main1_500"
                    app:layout_constraintStart_toEndOf="@id/bottom_nav_bar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/new_contact"
                    android:onClick="@{onNewContactClicked}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|bottom"
                    android:layout_margin="16dp"
                    android:src="@drawable/user_plus"
                    android:contentDescription="@string/content_description_contact_create"
                    app:tint="?attr/color_on_main"
                    app:backgroundTint="?attr/color_main1_500"
                    app:shapeAppearanceOverlay="@style/rounded"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/contacts_nav_container"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="@dimen/sliding_pane_right_fragment_width"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:defaultNavHost="false"
            app:navGraph="@navigation/contacts_nav_graph"/>

    </androidx.slidingpanelayout.widget.SlidingPaneLayout>

</layout>