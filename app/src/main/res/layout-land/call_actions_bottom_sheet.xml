<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="transferCallClickListener"
            type="View.OnClickListener" />
        <variable
            name="newCallClickListener"
            type="View.OnClickListener" />
        <variable
            name="callsListClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
        <variable
            name="callsViewModel"
            type="org.linphone.ui.call.viewmodel.CallsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/call_all_actions_menu_height"
        android:clickable="true"
        android:focusable="true"
        android:background="@drawable/shape_call_bottom_sheet_background"
        android:visibility="visible"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/call_main_actions_menu_height"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <include
            android:id="@+id/main_actions"
            layout="@layout/call_actions_generic"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_main_actions_menu_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            bind:viewModel="@{viewModel}" />

        <ImageView
            android:id="@+id/transfer"
            android:onClick="@{transferCallClickListener}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/phone_transfer"
            android:contentDescription="@string/call_action_transfer"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/transfer_label"
            app:layout_constraintStart_toStartOf="@id/transfer_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <ImageView
            android:id="@+id/new_call"
            android:onClick="@{newCallClickListener}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/phone_plus"
            android:contentDescription="@string/call_action_start_new_call"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/new_call_label"
            app:layout_constraintStart_toStartOf="@id/new_call_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <ImageView
            android:id="@+id/calls_list"
            android:onClick="@{callsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/phone_list"
            android:contentDescription="@string/call_action_go_to_calls_list"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/calls_list_label"
            app:layout_constraintStart_toStartOf="@id/calls_list_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/calls_count"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:layout_marginStart="40dp"
            android:layout_marginTop="25dp"
            android:text="@{String.valueOf(callsViewModel.callsCount), default=`1`}"
            android:visibility="@{callsViewModel.callsCount > 1 ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:layout_constraintStart_toStartOf="@id/calls_list"
            app:layout_constraintEnd_toEndOf="@id/calls_list"/>

        <ImageView
            android:id="@+id/numpad"
            android:onClick="@{() -> viewModel.showNumpad()}"
            android:enabled="@{!viewModel.isPaused &amp;&amp; !viewModel.isPausedByRemote}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/numpad"
            android:contentDescription="@string/call_action_show_dialer"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/numpad_label"
            app:layout_constraintStart_toStartOf="@id/numpad_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <ImageView
            android:id="@+id/chat"
            android:onClick="@{() -> viewModel.createConversation()}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/envelope_simple"
            android:contentDescription="@string/call_action_show_messages"
            android:enabled="@{!viewModel.operationInProgress}"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/chat_label"
            app:layout_constraintStart_toStartOf="@id/chat_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/chat_room_creation_in_progress"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:indeterminate="true"
            android:visibility="@{viewModel.operationInProgress ? View.VISIBLE : View.GONE}"
            app:indicatorColor="?attr/color_main1_500"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toTopOf="@id/chat"
            app:layout_constraintStart_toStartOf="@id/chat"
            app:layout_constraintEnd_toEndOf="@id/chat"
            app:layout_constraintBottom_toBottomOf="@id/chat"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/unread_messages_count"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:layout_marginStart="40dp"
            android:layout_marginTop="25dp"
            android:text="0"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:layout_constraintStart_toStartOf="@id/chat"
            app:layout_constraintEnd_toEndOf="@id/chat"/>

        <ImageView
            android:id="@+id/pause_call"
            android:onClick="@{() -> viewModel.togglePause()}"
            android:enabled="@{viewModel.canBePaused}"
            android:selected="@{viewModel.isPaused}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_green"
            android:padding="@dimen/call_button_icon_padding"
            android:contentDescription="@{viewModel.isPaused ? @string/call_action_resume_call : @string/call_action_pause_call}"
            android:src="@{viewModel.isPaused ? @drawable/play : @drawable/pause, default=@drawable/pause}"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/pause_call_label"
            app:layout_constraintStart_toStartOf="@id/pause_call_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <ImageView
            android:id="@+id/record_call"
            android:onClick="@{() -> viewModel.toggleRecording()}"
            android:enabled="@{viewModel.isRecordingEnabled &amp;&amp; !viewModel.isPaused &amp;&amp; !viewModel.isPausedByRemote}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:background="@drawable/in_call_button_background_red"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/record_fill"
            android:contentDescription="@string/call_action_record_call"
            android:selected="@{viewModel.isRecording()}"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="@id/record_call_label"
            app:layout_constraintStart_toStartOf="@id/record_call_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:tint="@color/in_call_button_tint_color" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/transfer_label"
            android:onClick="@{transferCallClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_transfer"
            app:layout_constraintEnd_toStartOf="@id/new_call_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/transfer"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/new_call_label"
            android:onClick="@{newCallClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_start_new_call"
            app:layout_constraintEnd_toStartOf="@id/calls_list_label"
            app:layout_constraintStart_toEndOf="@id/transfer_label"
            app:layout_constraintTop_toBottomOf="@id/new_call"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/calls_list_label"
            android:onClick="@{callsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_go_to_calls_list"
            app:layout_constraintEnd_toStartOf="@id/numpad_label"
            app:layout_constraintStart_toEndOf="@id/new_call_label"
            app:layout_constraintTop_toBottomOf="@id/calls_list"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/numpad_label"
            android:onClick="@{() -> viewModel.showNumpad()}"
            android:enabled="@{!viewModel.isPaused &amp;&amp; !viewModel.isPausedByRemote}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_show_dialer"
            app:layout_constraintEnd_toStartOf="@id/chat_label"
            app:layout_constraintStart_toEndOf="@id/calls_list_label"
            app:layout_constraintTop_toBottomOf="@id/numpad" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/chat_label"
            style="@style/in_call_extra_action_label_style"
            android:onClick="@{() -> viewModel.createConversation()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_show_messages"
            android:enabled="@{!viewModel.operationInProgress}"
            app:layout_constraintEnd_toStartOf="@id/pause_call_label"
            app:layout_constraintStart_toEndOf="@id/numpad_label"
            app:layout_constraintTop_toBottomOf="@id/chat" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/pause_call_label"
            android:onClick="@{() -> viewModel.togglePause()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:enabled="@{viewModel.canBePaused}"
            android:selected="@{viewModel.isPaused}"
            android:text="@{viewModel.isPaused ? @string/call_action_resume_call : @string/call_action_pause_call, default=@string/call_action_pause_call}"
            app:layout_constraintEnd_toStartOf="@id/record_call_label"
            app:layout_constraintStart_toEndOf="@id/chat_label"
            app:layout_constraintTop_toBottomOf="@id/pause_call" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/record_call_label"
            android:onClick="@{() -> viewModel.toggleRecording()}"
            android:enabled="@{viewModel.isRecordingEnabled &amp;&amp; !viewModel.isPaused &amp;&amp; !viewModel.isPausedByRemote}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_record_call"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/pause_call_label"
            app:layout_constraintTop_toBottomOf="@id/record_call" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>