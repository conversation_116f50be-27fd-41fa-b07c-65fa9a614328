<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.meetings.viewmodel.MeetingWaitingRoomViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_900">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/half_screen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.50" />

        <ImageView
            android:id="@+id/back"
            android:onClick="@{backClickListener}"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/top_bar_height"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@{viewModel.subject, default=`Meeting with John Doe`}"
            android:textColor="@color/bc_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="@id/back"
            app:layout_constraintBottom_toTopOf="@id/subtitle"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:text="@{viewModel.dateTime, default=`Wed. 25 Oct. 14:00 - 15:00`}"
            android:textSize="12sp"
            android:textColor="@color/bc_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toBottomOf="@id/back"/>

        <View
            android:id="@+id/no_video_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/shape_squircle_gray_600_background"
            app:layout_constraintTop_toBottomOf="@id/back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/half_screen"
            app:layout_constraintBottom_toTopOf="@id/toggle_mute_mic" />

        <include
            android:id="@+id/avatar"
            android:layout_width="@dimen/avatar_in_call_size"
            android:layout_height="@dimen/avatar_in_call_size"
            layout="@layout/contact_avatar_huge"
            bind:model="@{viewModel.selfAvatar}"
            app:layout_constraintEnd_toEndOf="@id/no_video_background"
            app:layout_constraintStart_toStartOf="@id/no_video_background"
            app:layout_constraintTop_toTopOf="@id/no_video_background"
            app:layout_constraintBottom_toBottomOf="@id/no_video_background" />


        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_500"
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="8dp"
            android:text="@{viewModel.selfAvatar.name, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="20sp"
            app:layout_constraintBottom_toBottomOf="@id/no_video_background"
            app:layout_constraintStart_toStartOf="@id/no_video_background" />
        


        <ImageView
            android:id="@+id/toggle_mute_mic"
            android:onClick="@{() -> viewModel.toggleMuteMicrophone()}"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="20dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@{viewModel.isMicrophoneMuted ? @drawable/microphone_slash : @drawable/microphone, default=@drawable/microphone}"
            android:background="@drawable/in_call_button_background_red"
            android:contentDescription="@string/content_description_toggle_microphone"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintBottom_toBottomOf="@id/join"
            app:layout_constraintStart_toStartOf="@id/no_video_background"
            app:layout_constraintEnd_toStartOf="@id/change_audio_output" />

        <ImageView
            android:id="@+id/change_audio_output"
            android:onClick="@{() -> viewModel.changeAudioOutputDevice()}"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@{viewModel.isHeadsetEnabled ? @drawable/headset : viewModel.isBluetoothEnabled ? @drawable/bluetooth : viewModel.isSpeakerEnabled ? @drawable/speaker_high : @drawable/speaker_slash, default=@drawable/speaker_slash}"
            android:background="@drawable/in_call_button_background_red"
            android:contentDescription="@string/content_description_change_output_audio_device"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintBottom_toBottomOf="@id/join"
            app:layout_constraintStart_toEndOf="@id/toggle_mute_mic"
            app:layout_constraintEnd_toEndOf="@id/half_screen" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/primary_button_label_style"
            android:id="@+id/join"
            android:onClick="@{() -> viewModel.join()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="@dimen/screen_bottom_margin"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:text="@string/meeting_waiting_room_join"
            app:layout_constraintWidth_max="@dimen/button_max_width"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/half_screen"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/gray_900"
            android:visibility="@{viewModel.joining ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_300"
                android:id="@+id/joining_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/meeting_waiting_room_joining_title"
                android:textSize="22sp"
                android:textColor="@color/bc_white"
                android:gravity="center"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/joining_subtitle"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_300"
                android:id="@+id/joining_subtitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/meeting_waiting_room_joining_subtitle"
                android:textSize="14sp"
                android:textColor="@color/bc_white"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/joining_title"
                app:layout_constraintBottom_toTopOf="@id/joining_progress"/>

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/joining_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:indeterminate="true"
                app:indicatorColor="?attr/color_main1_500"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/joining_subtitle"
                app:layout_constraintBottom_toTopOf="@id/cancel"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/primary_button_label_style"
                android:id="@+id/cancel"
                android:onClick="@{() -> viewModel.cancel()}"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="@dimen/screen_bottom_margin"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="@string/meeting_waiting_room_cancel"
                app:layout_constraintWidth_max="@dimen/button_max_width"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>