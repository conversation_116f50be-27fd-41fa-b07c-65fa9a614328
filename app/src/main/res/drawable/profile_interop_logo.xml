<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="71dp"
    android:height="62dp"
    android:viewportWidth="71"
    android:viewportHeight="62">
  <path
      android:pathData="M41.868,11.55V34.649C41.868,35.415 41.563,36.149 41.022,36.69C40.48,37.232 39.746,37.536 38.98,37.536H12.401L9.099,42.609C9.092,42.617 9.084,42.623 9.075,42.629C8.557,43.069 7.899,43.311 7.219,43.311C6.795,43.31 6.377,43.217 5.993,43.037C5.494,42.807 5.072,42.438 4.778,41.975C4.483,41.511 4.328,40.973 4.331,40.424V11.55C4.331,10.784 4.635,10.049 5.177,9.508C5.718,8.966 6.453,8.662 7.219,8.662H38.98C39.746,8.662 40.48,8.966 41.022,9.508C41.563,10.049 41.868,10.784 41.868,11.55Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="66.137"
          android:startY="-10.699"
          android:endX="-17.264"
          android:endY="60.058"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF9730"/>
        <item android:offset="1" android:color="#B2FFB266"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M28.813,23.889H17.872C17.581,23.889 17.303,24.005 17.098,24.21C16.893,24.415 16.777,24.693 16.777,24.983V28.266C16.777,28.556 16.893,28.834 17.098,29.04C17.303,29.245 17.581,29.36 17.872,29.36H28.813C29.104,29.36 29.382,29.245 29.587,29.04C29.792,28.834 29.907,28.556 29.907,28.266V24.983C29.907,24.693 29.792,24.415 29.587,24.21C29.382,24.005 29.104,23.889 28.813,23.889ZM28.813,28.266H17.872V24.983H28.813V28.266ZM28.813,17.324H17.872C17.581,17.324 17.303,17.44 17.098,17.645C16.893,17.85 16.777,18.128 16.777,18.418V21.701C16.777,21.991 16.893,22.269 17.098,22.475C17.303,22.68 17.581,22.795 17.872,22.795H28.813C29.104,22.795 29.382,22.68 29.587,22.475C29.792,22.269 29.907,21.991 29.907,21.701V18.418C29.907,18.128 29.792,17.85 29.587,17.645C29.382,17.44 29.104,17.324 28.813,17.324ZM28.813,21.701H17.872V18.418H28.813V21.701ZM27.719,20.06C27.719,20.222 27.671,20.381 27.581,20.516C27.491,20.65 27.362,20.756 27.212,20.818C27.063,20.88 26.898,20.896 26.738,20.865C26.579,20.833 26.433,20.755 26.318,20.64C26.203,20.525 26.125,20.379 26.094,20.22C26.062,20.061 26.078,19.896 26.14,19.746C26.202,19.596 26.308,19.468 26.443,19.377C26.577,19.287 26.736,19.239 26.899,19.239C27.116,19.239 27.325,19.326 27.479,19.479C27.633,19.633 27.719,19.842 27.719,20.06ZM27.719,26.625C27.719,26.787 27.671,26.946 27.581,27.081C27.491,27.216 27.362,27.321 27.212,27.383C27.063,27.445 26.898,27.461 26.738,27.43C26.579,27.398 26.433,27.32 26.318,27.205C26.203,27.09 26.125,26.944 26.094,26.785C26.062,26.626 26.078,26.461 26.14,26.311C26.202,26.161 26.308,26.033 26.443,25.942C26.577,25.852 26.736,25.804 26.899,25.804C27.116,25.804 27.325,25.891 27.479,26.045C27.633,26.198 27.719,26.407 27.719,26.625Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M33.462,29.607V52.487C33.462,53.245 33.767,53.973 34.308,54.509C34.85,55.045 35.584,55.347 36.35,55.347H62.93L66.232,60.371C66.239,60.379 66.247,60.385 66.255,60.391C66.774,60.827 67.432,61.067 68.112,61.067C68.536,61.066 68.954,60.973 69.338,60.795C69.837,60.567 70.259,60.202 70.553,59.743C70.848,59.284 71.003,58.751 71,58.207V29.607C71,28.848 70.696,28.121 70.154,27.584C69.613,27.048 68.878,26.747 68.112,26.747H36.35C35.584,26.747 34.85,27.048 34.308,27.584C33.767,28.121 33.462,28.848 33.462,29.607Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="9.192"
          android:startY="7.569"
          android:endX="91.927"
          android:endY="78.436"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF9730"/>
        <item android:offset="1" android:color="#B2FFB266"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M45.956,42.43H58.113C58.436,42.43 58.745,42.558 58.973,42.786C59.201,43.014 59.329,43.323 59.329,43.646V47.293C59.329,47.615 59.201,47.924 58.973,48.153C58.745,48.381 58.436,48.508 58.113,48.508H45.956C45.633,48.508 45.324,48.381 45.096,48.153C44.868,47.924 44.74,47.615 44.74,47.293V43.646C44.74,43.323 44.868,43.014 45.096,42.786C45.324,42.558 45.633,42.43 45.956,42.43ZM45.956,47.293H58.113V43.646H45.956V47.293ZM45.956,35.135H58.113C58.436,35.135 58.745,35.263 58.973,35.491C59.201,35.719 59.329,36.029 59.329,36.351V39.998C59.329,40.321 59.201,40.63 58.973,40.858C58.745,41.086 58.436,41.214 58.113,41.214H45.956C45.633,41.214 45.324,41.086 45.096,40.858C44.868,40.63 44.74,40.321 44.74,39.998V36.351C44.74,36.029 44.868,35.719 45.096,35.491C45.324,35.263 45.633,35.135 45.956,35.135ZM45.956,39.998H58.113V36.351H45.956V39.998ZM47.171,38.175C47.171,38.355 47.225,38.531 47.325,38.681C47.425,38.831 47.568,38.948 47.734,39.017C47.901,39.086 48.084,39.104 48.261,39.069C48.438,39.034 48.6,38.947 48.728,38.819C48.855,38.692 48.942,38.529 48.978,38.353C49.013,38.176 48.995,37.992 48.926,37.826C48.857,37.659 48.74,37.517 48.59,37.417C48.44,37.316 48.264,37.263 48.083,37.263C47.841,37.263 47.61,37.359 47.438,37.53C47.268,37.701 47.171,37.933 47.171,38.175ZM47.171,45.469C47.171,45.65 47.225,45.826 47.325,45.976C47.425,46.126 47.568,46.243 47.734,46.312C47.901,46.381 48.084,46.399 48.261,46.363C48.438,46.328 48.6,46.241 48.728,46.114C48.855,45.986 48.942,45.824 48.978,45.647C49.013,45.47 48.995,45.287 48.926,45.12C48.857,44.954 48.74,44.811 48.59,44.711C48.44,44.611 48.264,44.557 48.083,44.557C47.841,44.557 47.61,44.653 47.438,44.824C47.268,44.995 47.171,45.227 47.171,45.469Z"
      android:fillColor="#ffffff"/>
</vector>
