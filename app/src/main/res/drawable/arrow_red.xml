<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M26.708,11.708L16.708,21.708C16.615,21.801 16.505,21.875 16.383,21.925C16.262,21.975 16.132,22.001 16.001,22.001C15.869,22.001 15.739,21.975 15.618,21.925C15.496,21.875 15.386,21.801 15.293,21.708L5.293,11.708C5.105,11.52 5,11.266 5,11.001C5,10.735 5.105,10.481 5.293,10.293C5.481,10.105 5.735,10 6.001,10C6.266,10 6.52,10.105 6.708,10.293L16.001,19.587L25.293,10.293C25.386,10.2 25.496,10.126 25.618,10.076C25.739,10.026 25.869,10 26.001,10C26.132,10 26.262,10.026 26.383,10.076C26.505,10.126 26.615,10.2 26.708,10.293C26.801,10.386 26.875,10.496 26.925,10.618C26.975,10.739 27.001,10.869 27.001,11.001C27.001,11.132 26.975,11.262 26.925,11.384C26.875,11.505 26.801,11.615 26.708,11.708Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.001"
          android:startY="22.001"
          android:endX="16.001"
          android:endY="10"
          android:type="linear">
        <item android:offset="0" android:color="#FFDD5F5F"/>
        <item android:offset="1" android:color="#FF2E3030"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
