<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:name="vector"
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <group
                android:name="rotationgroup"
                android:pivotX="12"
                android:pivotY="12">
                <path
                    android:name="path"
                    android:pathData="M 12.692 3.692 L 12.692 6.462 C 12.692 6.645 12.619 6.821 12.49 6.951 C 12.36 7.081 12.184 7.154 12 7.154 C 11.816 7.154 11.64 7.081 11.51 6.951 C 11.381 6.821 11.308 6.645 11.308 6.462 L 11.308 3.692 C 11.308 3.509 11.381 3.333 11.51 3.203 C 11.64 3.073 11.816 3 12 3 C 12.184 3 12.36 3.073 12.49 3.203 C 12.619 3.333 12.692 3.509 12.692 3.692 Z M 15.916 8.776 C 16.007 8.776 16.097 8.758 16.181 8.724 C 16.265 8.689 16.341 8.637 16.406 8.573 L 18.364 6.616 C 18.494 6.486 18.567 6.309 18.567 6.126 C 18.567 5.942 18.494 5.766 18.364 5.636 C 18.234 5.506 18.058 5.433 17.874 5.433 C 17.691 5.433 17.514 5.506 17.384 5.636 L 15.427 7.594 C 15.33 7.691 15.264 7.814 15.237 7.949 C 15.21 8.083 15.224 8.222 15.276 8.349 C 15.329 8.475 15.417 8.583 15.531 8.66 C 15.645 8.736 15.779 8.776 15.916 8.776 Z M 20.308 11.308 L 17.538 11.308 C 17.355 11.308 17.179 11.381 17.049 11.51 C 16.919 11.64 16.846 11.816 16.846 12 C 16.846 12.184 16.919 12.36 17.049 12.49 C 17.179 12.619 17.355 12.692 17.538 12.692 L 20.308 12.692 C 20.491 12.692 20.667 12.619 20.797 12.49 C 20.927 12.36 21 12.184 21 12 C 21 11.816 20.927 11.64 20.797 11.51 C 20.667 11.381 20.491 11.308 20.308 11.308 Z M 16.406 15.427 C 16.275 15.303 16.101 15.234 15.92 15.237 C 15.739 15.239 15.567 15.312 15.439 15.439 C 15.312 15.567 15.239 15.739 15.237 15.92 C 15.234 16.101 15.303 16.275 15.427 16.406 L 17.384 18.364 C 17.514 18.494 17.691 18.567 17.874 18.567 C 18.058 18.567 18.234 18.494 18.364 18.364 C 18.494 18.234 18.567 18.058 18.567 17.874 C 18.567 17.691 18.494 17.514 18.364 17.384 L 16.406 15.427 Z M 12 16.846 C 11.816 16.846 11.64 16.919 11.51 17.049 C 11.381 17.179 11.308 17.355 11.308 17.538 L 11.308 20.308 C 11.308 20.491 11.381 20.667 11.51 20.797 C 11.64 20.927 11.816 21 12 21 C 12.184 21 12.36 20.927 12.49 20.797 C 12.619 20.667 12.692 20.491 12.692 20.308 L 12.692 17.538 C 12.692 17.355 12.619 17.179 12.49 17.049 C 12.36 16.919 12.184 16.846 12 16.846 Z M 7.594 15.427 L 5.636 17.384 C 5.506 17.514 5.433 17.691 5.433 17.874 C 5.433 18.058 5.506 18.234 5.636 18.364 C 5.766 18.494 5.942 18.567 6.126 18.567 C 6.309 18.567 6.486 18.494 6.616 18.364 L 8.573 16.406 C 8.697 16.275 8.766 16.101 8.763 15.92 C 8.761 15.739 8.688 15.567 8.561 15.439 C 8.433 15.312 8.261 15.239 8.08 15.237 C 7.899 15.234 7.725 15.303 7.594 15.427 Z M 7.154 12 C 7.154 11.816 7.081 11.64 6.951 11.51 C 6.821 11.381 6.645 11.308 6.462 11.308 L 3.692 11.308 C 3.509 11.308 3.333 11.381 3.203 11.51 C 3.073 11.64 3 11.816 3 12 C 3 12.184 3.073 12.36 3.203 12.49 C 3.333 12.619 3.509 12.692 3.692 12.692 L 6.462 12.692 C 6.645 12.692 6.821 12.619 6.951 12.49 C 7.081 12.36 7.154 12.184 7.154 12 Z M 6.616 5.636 C 6.486 5.506 6.309 5.433 6.126 5.433 C 5.942 5.433 5.766 5.506 5.636 5.636 C 5.506 5.766 5.433 5.942 5.433 6.126 C 5.433 6.309 5.506 6.486 5.636 6.616 L 7.594 8.573 C 7.725 8.697 7.899 8.766 8.08 8.763 C 8.261 8.761 8.433 8.688 8.561 8.561 C 8.688 8.433 8.761 8.261 8.763 8.08 C 8.766 7.899 8.697 7.725 8.573 7.594 L 6.616 5.636 Z"
                    android:fillColor="#5D1A8B"/>
            </group>
        </vector>
    </aapt:attr>
    <target android:name="rotationgroup">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="rotation"
                android:duration="3000"
                android:valueFrom="0"
                android:valueTo="360"
                android:valueType="floatType"
                android:repeatCount="infinite"
                android:interpolator="@android:anim/linear_interpolator"/>
        </aapt:attr>
    </target>
</animated-vector>
