<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M5.292,20.292L15.292,10.292C15.385,10.199 15.495,10.125 15.616,10.075C15.738,10.025 15.868,9.999 15.999,9.999C16.131,9.999 16.261,10.025 16.382,10.075C16.504,10.125 16.614,10.199 16.707,10.292L26.707,20.292C26.895,20.48 27,20.734 27,20.999C27,21.265 26.895,21.519 26.707,21.707C26.519,21.895 26.265,22 25.999,22C25.734,22 25.48,21.895 25.292,21.707L15.999,12.413L6.707,21.707C6.614,21.8 6.504,21.874 6.382,21.924C6.261,21.974 6.131,22 5.999,22C5.868,22 5.738,21.974 5.617,21.924C5.495,21.874 5.385,21.8 5.292,21.707C5.199,21.614 5.125,21.504 5.075,21.382C5.025,21.261 4.999,21.131 4.999,20.999C4.999,20.868 5.025,20.738 5.075,20.617C5.125,20.495 5.199,20.385 5.292,20.292Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="15.999"
          android:startY="9.999"
          android:endX="15.999"
          android:endY="22"
          android:type="linear">
        <item android:offset="0" android:color="#FF85E4A8"/>
        <item android:offset="1" android:color="#FF2E3030"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
