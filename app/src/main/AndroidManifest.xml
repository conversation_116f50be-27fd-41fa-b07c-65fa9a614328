<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- To be able to display contacts list & match calling/called numbers -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <!-- Starting Android 13 we need to ask notification permission -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- Needed for full screen intent in incoming call notifications -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <!-- To vibrate while receiving an incoming call -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Needed for foreground service
    (https://developer.android.com/guide/components/foreground-services) -->
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <!-- Needed for Android 14
    https://developer.android.com/about/versions/14/behavior-changes-14#fgs-types -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <!-- Required for foreground service started when a push is being received,
    without it app won't be able to access network if data saver is ON (for example) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <!-- Needed to keep a permanent foreground service and keep app alive to be able to receive
    calls for third party accounts for which push notifications aren't available,
    and starting Android 15 dataSync is limited to 6 hours per day
    and can't be used with RECEIVE_BOOT_COMPLETED intent either -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />

    <!-- Needed for auto start at boot if keep alive service is enabled -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name="org.linphone.LinphoneApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:enableOnBackInvokedCallback="true"
        android:localeConfig="@xml/locales_config"
        android:theme="@style/Theme.Linphone"
        android:appCategory="social"
        tools:targetApi="35">

        <!-- Required for call notifications to be displayed in Android auto -->
        <meta-data
            android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc"/>

        <!--<meta-data
            android:name="androidx.car.app.minCarApiLevel"
            android:value="1"/>-->

        <activity
            android:name="org.linphone.ui.splash.SplashActivity"
            android:theme="@style/Theme.Linphone"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="org.linphone.ui.main.MainActivity"
            android:theme="@style/AppSplashScreenTheme"
            android:windowSoftInputMode="adjustResize"
            android:exported="true"
            android:launchMode="singleTask">

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />

            

            <intent-filter>
                <action android:name="android.intent.action.VIEW_LOCUS" />
            </intent-filter>


            <intent-filter>
                <action android:name="android.intent.action.DIAL" />
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.CALL" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="tel" />
                <data android:scheme="sip" />
                <data android:scheme="callto" />
                <data android:scheme="sips" />
                <data android:scheme="sip-linphone" />
                <data android:scheme="sips-linphone" />
                <data android:scheme="linphone-sip" />
                <data android:scheme="linphone-sips" />
                <data android:scheme="linphone-config" />
            </intent-filter>

        </activity>

        

        

        <!-- File viewer activities removed with chat functionality -->
        <!-- <activity
            android:name="org.linphone.ui.fileviewer.MediaViewerActivity"
            android:windowSoftInputMode="adjustResize"
            android:launchMode="singleTask"
            android:resizeableActivity="true" />

        <activity
            android:name="org.linphone.ui.fileviewer.FileViewerActivity"
            android:windowSoftInputMode="adjustResize"
            android:launchMode="singleTask"
            android:resizeableActivity="true" /> -->

        <activity
            android:name="org.linphone.ui.call.CallActivity"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/Theme.LinphoneInCall"
            android:launchMode="singleTask"
            android:turnScreenOn="true"
            android:showWhenLocked="true"
            android:resizeableActivity="true"
            android:supportsPictureInPicture="true" />

        <!-- Services -->

        <service
            android:name="org.linphone.core.CoreInCallService"
            android:exported="false"
            android:foregroundServiceType="phoneCall|microphone"
            android:stopWithTask="false"
            android:label="@string/app_name" />

        <service
            android:name="org.linphone.core.CorePushService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false"
            android:label="@string/app_name" />

        <!-- File transfer service removed with chat functionality -->
        <!-- <service
            android:name="org.linphone.core.CoreFileTransferService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false"
            android:label="@string/app_name" /> -->

        <service android:name="org.linphone.core.tools.firebase.FirebaseMessaging"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service
            android:name="org.linphone.core.CoreKeepAliveThirdPartyAccountsService"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:stopWithTask="false"
            android:label="@string/app_name">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="Needed to keep app alive to be able to receive calls from third party SIP servers for which push notifications aren't available." />
        </service>

        <!--<service
            android:name="org.linphone.telecom.auto.AndroidAutoService"
            android:exported="true">
            <intent-filter>
                <action android:name="androidx.car.app.CarAppService"/>
                <category android:name="androidx.car.app.category.CALLING"/>
            </intent-filter>
        </service>-->

        <!-- Receivers -->

        <receiver android:name="org.linphone.core.CorePushReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="org.linphone.core.action.PUSH_RECEIVED"/>
            </intent-filter>
        </receiver>

        <receiver
            android:name="org.linphone.notifications.NotificationBroadcastReceiver"
            android:enabled="true"
            android:exported="false" />

        <receiver android:name="org.linphone.core.BootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
            </intent-filter>
        </receiver>

        <!-- Providers -->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="@string/file_provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

    </application>

</manifest>