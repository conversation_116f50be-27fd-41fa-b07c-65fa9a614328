
package org.linphone.notifications

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import org.linphone.LinphoneApplication.Companion.coreContext
import org.linphone.core.tools.Log

class NotificationBroadcastReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "[Notification Broadcast Receiver]"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val notificationId = intent.getIntExtra(NotificationsManager.INTENT_NOTIF_ID, 0)
        Log.i(
            "$TAG Got notification broadcast for ID [$notificationId]"
        )

        // Wait for coreContext to be ready to handle intent
        while (!coreContext.isReady()) {
            Thread.sleep(50)
        }

        if (intent.action == NotificationsManager.INTENT_ANSWER_CALL_NOTIF_ACTION || intent.action == NotificationsManager.INTENT_HANGUP_CALL_NOTIF_ACTION) {
            handleCallIntent(intent, notificationId)
        } else if (intent.action == NotificationsManager.INTENT_REPLY_MESSAGE_NOTIF_ACTION || intent.action == NotificationsManager.INTENT_MARK_MESSAGE_AS_READ_NOTIF_ACTION) {
            Log.i("$TAG Chat functionality completely removed - ignoring chat intent")
        }
    }

    private fun handleCallIntent(intent: Intent, notificationId: Int) {
        val remoteSipAddress = intent.getStringExtra(NotificationsManager.INTENT_REMOTE_ADDRESS)
        if (remoteSipAddress == null) {
            Log.e("$TAG Remote SIP address is null for call notification ID [$notificationId]")
            return
        }

        coreContext.postOnCoreThread { core ->
            // Try multiple methods to find the call
            var call = core.calls.find {
                it.remoteAddress.asStringUriOnly() == remoteSipAddress
            }
            
            // If not found, try with weakEqual comparison
            if (call == null) {
                val targetAddress = core.interpretUrl(remoteSipAddress, false)
                if (targetAddress != null) {
                    call = core.calls.find {
                        it.remoteAddress.weakEqual(targetAddress)
                    }
                }
            }
            
            // If still not found, try getCallByRemoteAddress2
            if (call == null) {
                val targetAddress = core.interpretUrl(remoteSipAddress, false)
                if (targetAddress != null) {
                    call = core.getCallByRemoteAddress2(targetAddress)
                }
            }
            
            // If still not found, log all current calls for debugging
            if (call == null) {
                Log.e("$TAG Couldn't find call from remote address [$remoteSipAddress]")
                
                // Check if there's a current call we can use as fallback
                val currentCall = core.currentCall
                if (currentCall != null) {
                    call = currentCall
                } else if (core.callsNb > 0) {
                    call = core.calls[0]
                }
            }
            
            if (call != null) {
                if (intent.action == NotificationsManager.INTENT_ANSWER_CALL_NOTIF_ACTION) {
                    if (call.state == org.linphone.core.Call.State.IncomingReceived || 
                        call.state == org.linphone.core.Call.State.IncomingEarlyMedia
                    ) {
                        coreContext.answerCall(call)
                    } else {
                        // Cancel the notification since we can't answer the call
                        coreContext.notificationsManager.removeIncomingCallNotification()
                        coreContext.notificationsManager.cancelNotification(notificationId)
                        
                        // Force cancel using Android's NotificationManager as backup
                        coreContext.postOnMainThread {
                            val androidNotificationManager = coreContext.context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                            androidNotificationManager.cancel(notificationId)
                            androidNotificationManager.cancel(1) // INCOMING_CALL_ID
                        }
                    }
                } else {
                    coreContext.terminateCall(call)
                }
            } else {
                Log.w("$TAG No call found for notification action, this might be a hanging notification")

                // Use the new force cleanup method for hanging notifications
                coreContext.notificationsManager.forceCleanupHangingCallNotifications()

                // Also immediately cancel the specific notification that was clicked
                coreContext.postOnMainThread {
                    val androidNotificationManager = coreContext.context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                    androidNotificationManager.cancel(notificationId)
                    // Also try canceling the standard incoming call ID
                    androidNotificationManager.cancel(1) // INCOMING_CALL_ID
                    Log.i("$TAG Immediately canceled notification [$notificationId] and incoming call notification")
                }
            }
        }
    }

    // Chat intent handling completely removed

    // Chat message text extraction completely removed
}
