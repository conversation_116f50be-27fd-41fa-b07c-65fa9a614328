
package org.linphone.notifications

import android.Manifest
import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service.STOP_FOREGROUND_REMOVE
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.Person
import androidx.core.app.RemoteInput
import androidx.core.app.TaskStackBuilder
import androidx.core.content.LocusIdCompat
import androidx.navigation.NavDeepLinkBuilder
import org.linphone.LinphoneApplication.Companion.coreContext
import com.phantom.caller.R
import org.linphone.compatibility.Compatibility
import org.linphone.contacts.AvatarGenerator
import org.linphone.contacts.ContactsManager.ContactsListener
import org.linphone.contacts.getAvatarBitmap
import org.linphone.contacts.getPerson
import org.linphone.core.Address
import org.linphone.core.Call
import org.linphone.core.ChatMessage
import org.linphone.core.ChatRoom
import org.linphone.core.ConferenceParams
import org.linphone.core.Core
import org.linphone.core.CoreInCallService
import org.linphone.core.CoreKeepAliveThirdPartyAccountsService
import org.linphone.core.CoreListenerStub
import org.linphone.core.Factory
import org.linphone.core.Friend
import org.linphone.core.MediaDirection
import org.linphone.core.tools.Log
import org.linphone.ui.call.CallActivity
import org.linphone.ui.main.MainActivity
import org.linphone.utils.AppUtils
import org.linphone.utils.FileUtils
import org.linphone.utils.LinphoneUtils

class NotificationsManager
    @MainThread
    constructor(private val context: Context) {
    companion object {
        private const val TAG = "[Notifications Manager]"

        const val INTENT_HANGUP_CALL_NOTIF_ACTION = "org.linphone.HANGUP_CALL_ACTION"
        const val INTENT_ANSWER_CALL_NOTIF_ACTION = "org.linphone.ANSWER_CALL_ACTION"
        const val INTENT_REPLY_MESSAGE_NOTIF_ACTION = "org.linphone.REPLY_ACTION"
        const val INTENT_MARK_MESSAGE_AS_READ_NOTIF_ACTION = "org.linphone.MARK_AS_READ_ACTION"
        const val INTENT_NOTIF_ID = "NOTIFICATION_ID"

        const val KEY_TEXT_REPLY = "key_text_reply"
        const val INTENT_LOCAL_IDENTITY = "LOCAL_IDENTITY"
        const val INTENT_REMOTE_ADDRESS = "REMOTE_ADDRESS"

        const val CHAT_TAG = "Chat"
        private const val MISSED_CALL_TAG = "Missed call"
        const val CHAT_NOTIFICATIONS_GROUP = "CHAT_NOTIF_GROUP"

        private const val INCOMING_CALL_ID = 1
        private const val DUMMY_NOTIF_ID = 3
        private const val KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID = 5
        private const val MISSED_CALL_ID = 10
    }

    private var currentInCallServiceNotificationId = -1
    private var currentKeepAliveThirdPartyAccountsForegroundServiceNotificationId = -1

    private var currentlyRingingCallRemoteAddress: Address? = null

    private val notificationManager: NotificationManagerCompat by lazy {
        NotificationManagerCompat.from(context)
    }

    private var inCallService: CoreInCallService? = null
    private var inCallServiceForegroundNotificationPublished = false
    private var waitForInCallServiceForegroundToStopIt = false

    private var keepAliveService: CoreKeepAliveThirdPartyAccountsService? = null

    private val callNotificationsMap: HashMap<String, Notifiable> = HashMap()
    // Chat notifications completely removed
    private val previousChatNotifications: ArrayList<Int> = arrayListOf()

    private val notificationsMap = HashMap<Int, Notification>()

    // Chat functionality completely removed
    private var currentlyDisplayedIncomingCallFragment: Boolean = false

    private lateinit var mediaPlayer: MediaPlayer

    private val contactsListener = object : ContactsListener {
        @WorkerThread
        override fun onContactsLoaded() { }

        @WorkerThread
        override fun onContactFoundInRemoteDirectory(friend: Friend) {
            val addresses = friend.addresses
            Log.i(
                "$TAG Found contact [${friend.name}] in remote directory with [${addresses.size}] addresses"
            )

            for ((remoteAddress, notifiable) in callNotificationsMap.entries) {
                val parsedAddress = Factory.instance().createAddress(remoteAddress)
                parsedAddress ?: continue
                val addressMatch = addresses.find {
                    it.weakEqual(parsedAddress)
                }
                if (addressMatch != null) {
                    Log.i(
                        "$TAG Found call [${addressMatch.asStringUriOnly()}] with contact in notifications, updating it"
                    )
                    updateCallNotification(notifiable, addressMatch, friend)
                }
            }

            // Chat notifications completely removed
        }
    }

    private val coreListener = object : CoreListenerStub() {
        @WorkerThread
        override fun onCallStateChanged(
            core: Core,
            call: Call,
            state: Call.State?,
            message: String
        ) {
            val currentState = call.state
            Log.i("$TAG Call state changed: [$currentState]")
            when (currentState) {
                Call.State.IncomingReceived, Call.State.IncomingEarlyMedia -> {
                    Log.i(
                        "$TAG Showing incoming call notification for [${call.remoteAddress.asStringUriOnly()}]"
                    )
                    showCallNotification(call, true)
                }
                Call.State.OutgoingInit -> {
                    Log.i(
                        "$TAG Showing outgoing call notification for [${call.remoteAddress.asStringUriOnly()}]"
                    )
                    showCallNotification(call, false)
                }
                Call.State.Connected,
                Call.State.StreamsRunning -> {
                    if (currentState == Call.State.Connected && call.dir == Call.Dir.Incoming) {
                        Log.i(
                            "$TAG Connected call was incoming (so it was answered), removing incoming call notification"
                        )
                        removeIncomingCallNotification()
                    }

                    if (currentState == Call.State.Connected || call.dir == Call.Dir.Incoming) {
                        Log.i(
                            "$TAG Showing connected call notification for [${call.remoteAddress.asStringUriOnly()}]"
                        )
                        showCallNotification(call, false)
                    }
                }
                Call.State.Updating -> {
                    val notifiable = getNotifiableForCall(call)
                    if (notifiable.notificationId == currentInCallServiceNotificationId) {
                        Log.i(
                            "$TAG Update foreground Service type in case video was enabled/disabled since last time"
                        )
                        startInCallForegroundService(call)
                    }
                }
                Call.State.End, Call.State.Error -> {
                    val remoteSipAddress = call.remoteAddress
                    val isError = call.state == Call.State.Error

                    if (isError) {
                        Log.i("$TAG Call ended with ERROR for [${remoteSipAddress.asStringUriOnly()}], performing aggressive cleanup")
                        logAllActiveNotifications() // Debug: see what notifications exist before cleanup
                    } else {
                        Log.i("$TAG Call ended normally for [${remoteSipAddress.asStringUriOnly()}]")
                    }

                    if (call.dir == Call.Dir.Incoming && currentlyRingingCallRemoteAddress?.weakEqual(
                            remoteSipAddress
                        ) == true
                    ) {
                        Log.i(
                            "$TAG Incoming call has been declined, cancelling incoming call notification"
                        )
                        if (waitForInCallServiceForegroundToStopIt) {
                            Log.w("$TAG We are waiting for service to be started as foreground, starting it now")
                            showCallNotification(call, false)
                        }
                        removeIncomingCallNotification()
                    } else {
                        Log.i(
                            "$TAG Removing terminated/declined call notification for [${remoteSipAddress.asStringUriOnly()}]"
                        )
                        if (waitForInCallServiceForegroundToStopIt) {
                            Log.w("$TAG We are waiting for service to be started as foreground, starting it now")
                            showCallNotification(call, false)
                        }
                        dismissCallNotification(call)
                    }

                    // For error cases, perform additional aggressive cleanup immediately
                    if (isError) {
                        Log.i("$TAG Performing immediate aggressive cleanup for error case")

                        // CRITICAL: Get the actual notification ID for this specific call
                        val callNotificationId = getNotificationIdForCall(call)
                        Log.i("$TAG Error case: This call's notification ID is [$callNotificationId]")

                        coreContext.postOnMainThread {
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                Log.i("$TAG Error case: force canceling any remaining call notifications")
                                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                                try {
                                    // CRITICAL: Cancel the specific notification ID for this call
                                    androidNotificationManager.cancel(callNotificationId)
                                    Log.i("$TAG Error case: CRITICAL - canceled specific call notification [$callNotificationId]")

                                    // Cancel all known call notification IDs
                                    androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
                                    androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

                                    // Cancel the current foreground service notification if any
                                    if (currentInCallServiceNotificationId != -1) {
                                        androidNotificationManager.cancel(currentInCallServiceNotificationId)
                                        Log.i("$TAG Error case: canceled foreground service notification [$currentInCallServiceNotificationId]")
                                    }

                                    // Scan and cancel any remaining call-related notifications
                                    val activeNotifications = androidNotificationManager.activeNotifications
                                    Log.i("$TAG Error case: scanning ${activeNotifications.size} active notifications")

                                    for (notification in activeNotifications) {
                                        Log.i("$TAG Error case: checking notification [${notification.id}] with tag [${notification.tag}]")

                                        val isCallNotification = notification.notification?.let { notif ->
                                            val hasCallCategory = notif.category == NotificationCompat.CATEGORY_CALL
                                            val hasCallChannel = notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                                                notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                            val hasCallActions = notif.actions?.any { action ->
                                                val title = action.title?.toString()?.lowercase() ?: ""
                                                title.contains("hang") || title.contains("answer") || title.contains("decline")
                                            } == true

                                            Log.i("$TAG Error case notification [${notification.id}]: category=$hasCallCategory, channel=$hasCallChannel, actions=$hasCallActions")
                                            hasCallCategory || hasCallChannel || hasCallActions
                                        } ?: false

                                        if (isCallNotification) {
                                            Log.i("$TAG Error case: canceling hanging call notification [${notification.id}] with tag [${notification.tag}]")
                                            if (notification.tag.isNullOrEmpty()) {
                                                androidNotificationManager.cancel(notification.id)
                                            } else {
                                                androidNotificationManager.cancel(notification.tag, notification.id)
                                            }
                                        } else {
                                            Log.i("$TAG Error case: notification [${notification.id}] is not a call notification, skipping")
                                        }
                                    }

                                    // Nuclear option for error cases: if we still have notifications after 1 second, cancel ALL notifications
                                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                        val remainingNotifications = androidNotificationManager.activeNotifications
                                        val remainingCallNotifications = remainingNotifications.filter { notification ->
                                            notification.notification?.let { notif ->
                                                notif.category == NotificationCompat.CATEGORY_CALL ||
                                                notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                                notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                            } ?: false
                                        }

                                        if (remainingCallNotifications.isNotEmpty()) {
                                            Log.w("$TAG NUCLEAR OPTION: Still have ${remainingCallNotifications.size} call notifications after error cleanup, canceling ALL notifications")
                                            androidNotificationManager.cancelAll()
                                        }
                                    }, 1000) // 1 second delay for nuclear option

                                } catch (e: Exception) {
                                    Log.w("$TAG Exception during error case notification cleanup: $e")
                                }
                            }, 50) // Very short delay for error cases
                        }
                    }

                    // Force stop foreground service if this was the last call
                    if (core.callsNb == 0) {
                        Log.i("$TAG Last call ended, force stopping foreground service")
                        if (inCallServiceForegroundNotificationPublished) {
                            stopInCallForegroundService()
                        }

                        // Backup cleanup mechanism - ensure all notifications are cleaned up when last call ends
                        Log.i("$TAG Backup cleanup: triggering comprehensive notification cleanup since this was the last call")
                        coreContext.postOnCoreThreadDelayed({
                            Log.i("$TAG Backup cleanup: executing delayed cleanup for last call")
                            logAllActiveNotifications()

                            // Clean up all call notifications
                            cleanupAllCallNotifications()

                            // Force cleanup hanging notifications
                            forceCleanupHangingCallNotifications()

                            // Additional safety check after 2 seconds
                            coreContext.postOnMainThread {
                                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                    val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                                    val activeNotifications = androidNotificationManager.activeNotifications
                                    val callNotifications = activeNotifications.filter { notification ->
                                        notification.notification?.let { notif ->
                                            notif.category == NotificationCompat.CATEGORY_CALL ||
                                            notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                            notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                        } ?: false
                                    }

                                    if (callNotifications.isNotEmpty()) {
                                        Log.w("$TAG BACKUP NUCLEAR OPTION: Still have ${callNotifications.size} call notifications 2 seconds after last call ended, canceling ALL notifications")
                                        androidNotificationManager.cancelAll()
                                    } else {
                                        Log.i("$TAG Backup cleanup successful: no call notifications remaining")
                                    }
                                }, 2000) // 2 second final safety check
                            }
                        }, 300) // 300ms delay for backup cleanup

                        // Additional immediate cleanup for hanging notifications
                        coreContext.postOnMainThread {
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                Log.i("$TAG Immediate cleanup: force canceling any remaining call notifications")
                                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                                try {
                                    // Cancel all known call notification IDs
                                    androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
                                    androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

                                    // Cancel any active call notifications
                                    val activeNotifications = androidNotificationManager.activeNotifications
                                    for (notification in activeNotifications) {
                                        val isCallNotification = notification.notification?.let { notif ->
                                            notif.category == NotificationCompat.CATEGORY_CALL ||
                                            notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                            notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                        } ?: false

                                        if (isCallNotification) {
                                            Log.i("$TAG Immediate cleanup: canceling call notification [${notification.id}]")
                                            if (notification.tag.isNullOrEmpty()) {
                                                androidNotificationManager.cancel(notification.id)
                                            } else {
                                                androidNotificationManager.cancel(notification.tag, notification.id)
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.w("$TAG Exception during immediate notification cleanup: $e")
                                }
                            }, 100) // 100ms delay for immediate cleanup
                        }
                    }
                }
                Call.State.Released -> {
                    Log.i(
                        "$TAG Call released, ensuring notification is dismissed for [${call.remoteAddress.asStringUriOnly()}]"
                    )
                    // Always dismiss call notification when call is released
                    dismissCallNotification(call)
                    
                    if (LinphoneUtils.isCallLogMissed(call.callLog)) {
                        showMissedCallNotification(call)
                    }
                }
                else -> {
                }
            }
        }

        @WorkerThread
        override fun onLastCallEnded(core: Core) {
            Log.i("$TAG Last call ended")
            logAllActiveNotifications() // Debug: see what notifications exist when last call ends

            // Add a small delay to ensure all call state changes have been processed
            coreContext.postOnCoreThreadDelayed({
                Log.i("$TAG Processing last call ended cleanup after delay")

                // Double-check that there are really no calls left
                if (core.callsNb == 0) {
                    Log.i("$TAG Confirmed no calls remaining, proceeding with cleanup")

                    // Force stop foreground service immediately
                    Log.i("$TAG Force stopping foreground service since last call ended")
                    if (inCallServiceForegroundNotificationPublished) {
                        stopInCallForegroundService()
                    }

                    // Clean up all remaining call notifications
                    Log.i("$TAG Cleaning up all call notifications since last call ended")
                    cleanupAllCallNotifications()

                    // Additional safety: force stop service even if flag wasn't set
                    if (inCallService != null) {
                        Log.i("$TAG Additional safety: force stopping in-call service")
                        try {
                            inCallService?.stopForeground(STOP_FOREGROUND_REMOVE)
                            inCallService?.stopSelf()
                        } catch (e: Exception) {
                            Log.w("$TAG Exception while force stopping service: $e")
                        }
                    }

                    // Nuclear option: stop service using system service manager
                    Log.i("$TAG Nuclear option: stopping CoreInCallService using system")
                    try {
                        val serviceIntent = Intent(context, CoreInCallService::class.java)
                        context.stopService(serviceIntent)
                        Log.i("$TAG Successfully stopped CoreInCallService via system")
                    } catch (e: Exception) {
                        Log.w("$TAG Exception while stopping service via system: $e")
                    }

                    // Final safety check: force cancel all call-related notifications after a short delay
                    coreContext.postOnMainThread {
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            Log.i("$TAG Final safety check: force canceling any remaining call notifications")
                            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                            try {
                                // Cancel all known call notification IDs
                                androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
                                androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

                                // Cancel any active notifications that might be call-related
                                val activeNotifications = androidNotificationManager.activeNotifications
                                for (notification in activeNotifications) {
                                    // Check if this looks like a call notification
                                    val isCallNotification = notification.notification?.let { notif ->
                                        notif.category == NotificationCompat.CATEGORY_CALL ||
                                        notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                        notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                    } ?: false

                                    if (isCallNotification) {
                                        Log.i("$TAG Final cleanup: canceling call notification [${notification.id}]")
                                        if (notification.tag.isNullOrEmpty()) {
                                            androidNotificationManager.cancel(notification.id)
                                        } else {
                                            androidNotificationManager.cancel(notification.tag, notification.id)
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                Log.w("$TAG Exception during final notification cleanup: $e")
                            }
                        }, 500) // 500ms delay for final cleanup
                    }
                } else {
                    Log.w("$TAG onLastCallEnded called but there are still ${core.callsNb} calls, skipping cleanup")
                }
            }, 100) // 100ms delay to ensure all state changes are processed
        }

        // Chat message handling completely removed

        // Chat message reaction handling completely removed

        // Chat reaction removal handling completely removed

        // Chat room read handling completely removed
    }

    // Chat message listener completely removed

    init {
        for (notification in notificationManager.activeNotifications) {
            if (notification.tag.isNullOrEmpty()) {
                Log.w(
                    "$TAG Found existing (call?) notification [${notification.id}] without tag, cancelling it"
                )
                notificationManager.cancel(notification.id)
            } else if (notification.tag == CHAT_TAG) {
                Log.i(
                    "[Notifications Manager] Found existing chat notification [${notification.id}]"
                )
                previousChatNotifications.add(notification.id)
            }
        }
    }

    @AnyThread
    fun setIncomingCallFragmentCurrentlyDisplayed(visible: Boolean) {
        currentlyDisplayedIncomingCallFragment = visible
    }

    // Chat room display tracking completely removed

    @MainThread
    fun onInCallServiceStarted(service: CoreInCallService) {
        Log.i("$TAG Service has been started")
        inCallService = service

        coreContext.postOnCoreThread { core ->
            if (core.callsNb == 0) {
                Log.w("$TAG No call anymore, stopping service")
                if (waitForInCallServiceForegroundToStopIt) {
                    Log.w("$TAG Service wasn't started as foreground yet, doing it now using a dummy notification")
                    showDummyNotificationForCallService()
                }
                if (inCallServiceForegroundNotificationPublished) {
                    stopInCallForegroundService()
                } else {
                    Log.w("$TAG Foreground service notification wasn't published, shouldn't happen")
                }
            } else if (currentInCallServiceNotificationId == -1) {
                val call = core.currentCall ?: core.calls.first()
                Log.i(
                    "$TAG At least one call is running and no foreground Service notification was found, starting it using call [${call.remoteAddress.asStringUriOnly()}]"
                )

                Log.i("$TAG No notification found for this call, creating one now")
                showCallNotification(call, LinphoneUtils.isCallIncoming(call.state))
            }
        }
    }

    @MainThread
    fun onInCallServiceDestroyed() {
        Log.i("$TAG Service has been destroyed")
        stopInCallForegroundService()
        inCallService = null
    }

    @MainThread
    fun onKeepAliveServiceStarted(service: CoreKeepAliveThirdPartyAccountsService) {
        Log.i("$TAG Keep app alive for third party accounts Service has been started")
        keepAliveService = service
        startKeepAliveServiceForeground()
    }

    @MainThread
    fun onKeepAliveServiceDestroyed() {
        Log.i("$TAG Keep app alive for third party accounts Service has been destroyed")
        stopKeepAliveServiceForeground()
        keepAliveService = null
    }

    @MainThread
    private fun validateNotificationChannel(channelId: String): Boolean {
        val channel = notificationManager.getNotificationChannel(channelId)
        if (channel == null) {
            return false
        }
        
        val importance = channel.importance
        if (importance == NotificationManagerCompat.IMPORTANCE_NONE) {
            return false
        }
        
        return true
    }

    @MainThread
    private fun createChannels(clearPreviousChannels: Boolean) {
        if (clearPreviousChannels) {
            Log.w("$TAG We were asked to remove all existing notification channels")
            for (channel in notificationManager.notificationChannels) {
                try {
                    Log.i("$TAG Deleting notification channel ID [${channel.id}]")
                    notificationManager.deleteNotificationChannel(channel.id)
                } catch (e: Exception) {
                    Log.e("$TAG Failed to delete notification channel ID [${channel.id}]: $e")
                }
            }
        } else {
            try {
                val oldId = context.getString(R.string.notification_channel_incoming_call_id)
                val oldChannel = notificationManager.getNotificationChannel(oldId)
                if (oldChannel != null) {
                    Log.i("$TAG Deleting notification channel ID [$oldId]")
                    notificationManager.deleteNotificationChannel(oldId)
                }
            } catch (e: Exception) {
                Log.e("$TAG Failed to check if deprecated incoming call notification channel exists: $e")
            }
        }

        createThirdPartyAccountKeepAliveServiceChannel()
        createIncomingCallNotificationChannelWithoutRingtone()
        createMissedCallNotificationChannel()
        createActiveCallNotificationChannel()
        createMessageChannel()
    }

    @WorkerThread
    fun onCoreStarted(core: Core, clearChannels: Boolean) {
        Log.i("$TAG Core has been started")

        coreContext.postOnMainThread {
            createChannels(clearChannels)
        }

        core.addListener(coreListener)

        coreContext.contactsManager.addListener(contactsListener)

        // Chat message sound player completely removed
    }

    @WorkerThread
    fun onCoreStopped(core: Core) {
        Log.i("$TAG Getting destroyed, clearing foreground Service & call notifications")
        core.removeListener(coreListener)
        coreContext.contactsManager.removeListener(contactsListener)
    }

    @WorkerThread
    fun removeIncomingCallNotification() {
        if (currentInCallServiceNotificationId == INCOMING_CALL_ID) {
            if (inCallService != null) {
                Log.i(
                    "$TAG Service found, stopping it as foreground before cancelling notification"
                )
                inCallService?.stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                Log.w("$TAG Incoming call foreground notification Service wasn't found, weird...")
            }
            currentInCallServiceNotificationId = -1
        } else {
            Log.i(
                "$TAG Incoming call notification wasn't used to keep running Service as foreground"
            )
        }

        cancelNotification(INCOMING_CALL_ID)
        currentlyRingingCallRemoteAddress = null

        // Additional force cancellation using Android NotificationManager
        coreContext.postOnMainThread {
            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            androidNotificationManager.cancel(INCOMING_CALL_ID)
            Log.i("$TAG Force canceled incoming call notification using Android NotificationManager")
        }
    }

    @WorkerThread
    private fun showCallNotification(call: Call, isIncoming: Boolean, friend: Friend? = null) {
        val notifiable = getNotifiableForCall(call)

        val callNotificationIntent = Intent(context, CallActivity::class.java)
        callNotificationIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        if (isIncoming) {
            callNotificationIntent.putExtra("IncomingCall", true)
        } else {
            callNotificationIntent.putExtra("ActiveCall", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            callNotificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = createCallNotification(
            call,
            notifiable,
            pendingIntent,
            isIncoming,
            friend
        )
        if (isIncoming) {
            currentlyRingingCallRemoteAddress = call.remoteAddress
            if (currentInCallServiceNotificationId == -1) {
                Log.i("$TAG No current in-call foreground service notification found, using this one")
                showIncomingCallForegroundServiceNotification(notification)
            } else {
                notify(INCOMING_CALL_ID, notification)
            }
        } else {
            if (currentInCallServiceNotificationId == -1) {
                Log.i("$TAG No current in-call foreground service notification found, using this one")
                showInCallForegroundServiceNotification(call, notifiable, notification)
            } else {
                notify(notifiable.notificationId, notification)
            }
        }
    }

    @WorkerThread
    private fun showMissedCallNotification(call: Call) {
        val missedCallCount: Int = coreContext.core.missedCallsCount
        val body: String
        if (missedCallCount > 1) {
            body = context.getString(R.string.notification_missed_calls)
                .format(missedCallCount.toString())
            Log.i("$TAG Updating missed calls notification count to $missedCallCount")
        } else {
            val remoteAddress = call.callLog.remoteAddress
            val conferenceInfo = call.callLog.conferenceInfo
            body = if (conferenceInfo != null) {
                context.getString(R.string.notification_missed_group_call)
                    .format(conferenceInfo.subject ?: LinphoneUtils.getDisplayName(remoteAddress))
            } else {
                val friend: Friend? = coreContext.contactsManager.findContactByAddress(remoteAddress)
                context.getString(R.string.notification_missed_call)
                    .format(friend?.name ?: LinphoneUtils.getDisplayName(remoteAddress))
            }
            Log.i("$TAG Creating missed call notification with title [$body]")
        }

        val pendingIntent = NavDeepLinkBuilder(context)
            .setComponentName(MainActivity::class.java)
            .setGraph(R.navigation.main_nav_graph)
            .setDestination(R.id.historyListFragment)
            .createPendingIntent()

        val builder = NotificationCompat.Builder(
            context,
            context.getString(R.string.notification_channel_missed_call_id)
        )
            .setContentTitle(context.getString(R.string.notification_missed_call_title))
            .setContentText(body)
            .setSmallIcon(R.drawable.phone_x)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_MISSED_CALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setNumber(missedCallCount)
            .setContentIntent(pendingIntent)

        val notification = builder.build()
        notify(MISSED_CALL_ID, notification, MISSED_CALL_TAG)
    }

    @WorkerThread
    private fun showIncomingCallForegroundServiceNotification(notification: Notification) {
        Log.i("$TAG Trying to start foreground Service using incoming call notification")
        val service = inCallService
        if (service != null) {
            if (Compatibility.isPostNotificationsPermissionGranted(context)) {
                val channelId = context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                if (!validateNotificationChannel(channelId)) {
                    createChannels(false)
                }
                
                Log.i(
                    "$TAG Service found, starting it as foreground using notification ID [$INCOMING_CALL_ID] with type PHONE_CALL"
                )
                try {
                    Compatibility.startServiceForeground(
                        service,
                        INCOMING_CALL_ID,
                        notification,
                        Compatibility.FOREGROUND_SERVICE_TYPE_PHONE_CALL
                    )
                    notificationsMap[INCOMING_CALL_ID] = notification
                    currentInCallServiceNotificationId = INCOMING_CALL_ID
                    inCallServiceForegroundNotificationPublished = true

                    if (waitForInCallServiceForegroundToStopIt) {
                        stopInCallForegroundService()
                    }
                } catch (e: Exception) {
                    // Try to ensure notification channel exists and is enabled
                    coreContext.postOnMainThread {
                        createChannels(false)
                    }
                    // Still show the notification even if foreground service failed
                    notify(INCOMING_CALL_ID, notification)
                }
            } else {
                Log.e("$TAG POST_NOTIFICATIONS permission isn't granted, don't start foreground service!")
            }
        } else {
            Log.w("$TAG Core Foreground Service hasn't started yet...")
        }
    }

    @WorkerThread
    private fun startInCallForegroundService(call: Call) {
        if (LinphoneUtils.isCallIncoming(call.state)) {
            val notification = notificationsMap[INCOMING_CALL_ID]
            if (notification != null) {
                showIncomingCallForegroundServiceNotification(notification)
            } else {
                Log.w(
                    "$TAG Failed to find notification for incoming call with ID [$INCOMING_CALL_ID]"
                )
            }
            return
        }

        Log.i("$TAG Trying to start/update foreground Service using call notification")
        val service = inCallService
        if (service == null) {
            Log.w("$TAG Core Foreground Service hasn't started yet...")
            return
        }

        val channelId = context.getString(R.string.notification_channel_call_id)
        val channel = notificationManager.getNotificationChannel(channelId)
        val importance = channel?.importance ?: NotificationManagerCompat.IMPORTANCE_NONE
        if (importance == NotificationManagerCompat.IMPORTANCE_NONE) {
            Log.e("$TAG Calls channel has been disabled, can't start foreground service!")
            stopInCallForegroundService()
            return
        }

        val notifiable = getNotifiableForCall(call)
        val notificationId = notifiable.notificationId
        val notification = if (notificationsMap.containsKey(notificationId)) {
            notificationsMap[notificationId]
        } else if (notificationsMap.containsKey(INCOMING_CALL_ID)) {
            notificationsMap[INCOMING_CALL_ID]
        } else {
            Log.w("$TAG Failed to find a notification for call [${call.remoteAddress.asStringUriOnly()}] in map")
            null
        }
        if (notification == null) {
            Log.w(
                "$TAG No existing notification (ID [$notificationId]) found for current call [${call.remoteAddress.asStringUriOnly()}], aborting"
            )
            stopInCallForegroundService()
            return
        }
        Log.i("$TAG Found notification [$notificationId] for current Call")

        showInCallForegroundServiceNotification(call, notifiable, notification)
    }

    @WorkerThread
    private fun showInCallForegroundServiceNotification(call: Call, notifiable: Notifiable, notification: Notification) {
        val service = inCallService
        if (service == null) {
            Log.w("$TAG Core Foreground Service hasn't started yet...")
            return
        }

        var mask = Compatibility.FOREGROUND_SERVICE_TYPE_PHONE_CALL
        val callState = call.state
        if (!LinphoneUtils.isCallIncoming(callState) && !LinphoneUtils.isCallOutgoing(callState) && !LinphoneUtils.isCallEnding(
                callState
            )
        ) {
            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.RECORD_AUDIO
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                mask = mask or Compatibility.FOREGROUND_SERVICE_TYPE_MICROPHONE
                Log.i(
                    "$TAG RECORD_AUDIO permission has been granted, adding FOREGROUND_SERVICE_TYPE_MICROPHONE to foreground Service types mask"
                )
            }
            val isSendingVideo = when (call.currentParams.videoDirection) {
                MediaDirection.SendRecv, MediaDirection.SendOnly -> true
                else -> false
            }
            // Camera foreground service type removed - audio-only app
        }

        if (Compatibility.isPostNotificationsPermissionGranted(context)) {
            val channelId = context.getString(R.string.notification_channel_call_id)
            if (!validateNotificationChannel(channelId)) {
                createChannels(false)
            }
            
            Log.i(
                "$TAG Service found, starting it as foreground using notification ID [${notifiable.notificationId}] with type(s) [$mask]"
            )
            try {
                Compatibility.startServiceForeground(
                    service,
                    notifiable.notificationId,
                    notification,
                    mask
                )
                notificationsMap[notifiable.notificationId] = notification
                currentInCallServiceNotificationId = notifiable.notificationId
                inCallServiceForegroundNotificationPublished = true

                if (waitForInCallServiceForegroundToStopIt) {
                    stopInCallForegroundService()
                }
            } catch (e: Exception) {
                // Try to ensure notification channel exists and is enabled
                coreContext.postOnMainThread {
                    createChannels(false)
                }
                // Still show the notification even if foreground service failed
                notify(notifiable.notificationId, notification)
            }
        } else {
            Log.e("$TAG POST_NOTIFICATIONS permission isn't granted, don't start foreground service!")
        }
    }

    @AnyThread
    private fun showDummyNotificationForCallService() {
        val service = inCallService
        if (service != null) {
            val channelId = context.getString(R.string.notification_channel_call_id)
            val pendingIntent = TaskStackBuilder.create(context).run {
                addNextIntentWithParentStack(
                    Intent(context, MainActivity::class.java).apply {
                        action = Intent.ACTION_MAIN // Needed as well
                    }
                )
                getPendingIntent(
                    KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )!!
            }
            val builder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.linphone_notification)
                .setAutoCancel(false)
                .setOngoing(true)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setShowWhen(false)
                .setContentIntent(pendingIntent)
            val notification = builder.build()

            if (Compatibility.isPostNotificationsPermissionGranted(context)) {
                val channelId = context.getString(R.string.notification_channel_call_id)
                if (!validateNotificationChannel(channelId)) {
                    createChannels(false)
                }
                
                Log.i(
                    "$TAG Service found, starting it as foreground using dummy notification ID [$DUMMY_NOTIF_ID]"
                )
                try {
                    Compatibility.startServiceForeground(
                        service,
                        DUMMY_NOTIF_ID,
                        notification,
                        Compatibility.FOREGROUND_SERVICE_TYPE_PHONE_CALL
                    )
                    notificationsMap[INCOMING_CALL_ID] = notification
                    currentInCallServiceNotificationId = DUMMY_NOTIF_ID
                    inCallServiceForegroundNotificationPublished = true
                } catch (e: Exception) {
                    // Try to ensure notification channel exists and is enabled
                    coreContext.postOnMainThread {
                        createChannels(false)
                    }
                }
            } else {
                Log.e("$TAG POST_NOTIFICATIONS permission isn't granted, don't start foreground service!")
            }
        } else {
            Log.w("$TAG Core Foreground Service hasn't started yet...")
        }
    }

    @AnyThread
    private fun stopInCallForegroundService() {
        val notificationIdToCancel = currentInCallServiceNotificationId
        Log.i("$TAG stopInCallForegroundService called, notification ID to cancel: [$notificationIdToCancel]")

        // CRITICAL: Force cancel the notification IMMEDIATELY using NotificationManager
        // This ensures the notification is removed even if service operations fail
        coreContext.postOnMainThread {
            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            // Cancel the specific foreground service notification immediately
            if (notificationIdToCancel != -1) {
                Log.i("$TAG IMMEDIATE: Force canceling foreground service notification with ID [$notificationIdToCancel]")
                androidNotificationManager.cancel(notificationIdToCancel)
            }

            // Cancel all known call notification IDs immediately
            Log.i("$TAG IMMEDIATE: Force canceling all known call notification IDs")
            androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
            androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

            // CRITICAL: Also cancel any dynamic call notification IDs that might be active
            // Scan all active notifications and cancel any call-related ones immediately
            try {
                val activeNotifications = androidNotificationManager.activeNotifications
                Log.i("$TAG IMMEDIATE: Scanning ${activeNotifications.size} active notifications for call-related ones")

                for (notification in activeNotifications) {
                    val isCallNotification = notification.notification?.let { notif ->
                        notif.category == NotificationCompat.CATEGORY_CALL ||
                        notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                        notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                    } ?: false

                    if (isCallNotification) {
                        Log.i("$TAG IMMEDIATE: Force canceling call notification [${notification.id}] immediately")
                        if (notification.tag.isNullOrEmpty()) {
                            androidNotificationManager.cancel(notification.id)
                        } else {
                            androidNotificationManager.cancel(notification.tag, notification.id)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.w("$TAG Exception during immediate notification scan: $e")
            }
        }

        val service = inCallService
        if (service != null) {
            Log.i(
                "$TAG Stopping foreground Service (was using notification ID [$currentInCallServiceNotificationId])"
            )
            try {
                service.stopForeground(STOP_FOREGROUND_REMOVE)
                service.stopSelf()
                Log.i("$TAG Successfully stopped foreground service")
            } catch (e: Exception) {
                Log.e("$TAG Exception while stopping foreground service: $e")
                // Even if service stop fails, we already canceled the notification above
            }
        } else {
            Log.w("$TAG Can't stop foreground Service, no Service was found - but notification should already be canceled")
        }

        // Reset flags immediately
        inCallServiceForegroundNotificationPublished = false
        waitForInCallServiceForegroundToStopIt = false
        currentInCallServiceNotificationId = -1

        // Additional aggressive cleanup with multiple attempts
        coreContext.postOnMainThread {
            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            try {
                // Second attempt to cancel the specific notification (redundant but safe)
                if (notificationIdToCancel != -1) {
                    Log.i("$TAG Second attempt: Force canceling foreground service notification with ID [$notificationIdToCancel]")
                    androidNotificationManager.cancel(notificationIdToCancel)
                }

                // Scan and cancel any remaining call-related notifications
                val activeNotifications = androidNotificationManager.activeNotifications
                Log.i("$TAG Scanning ${activeNotifications.size} active notifications for call-related ones")

                for (notification in activeNotifications) {
                    val isCallNotification = notification.notification?.let { notif ->
                        notif.category == NotificationCompat.CATEGORY_CALL ||
                        notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                        notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id) ||
                        notif.actions?.any { action ->
                            action.title?.toString()?.contains("hang", ignoreCase = true) == true ||
                            action.title?.toString()?.contains("answer", ignoreCase = true) == true ||
                            action.title?.toString()?.contains("decline", ignoreCase = true) == true
                        } == true
                    } ?: false

                    if (isCallNotification) {
                        Log.i("$TAG Force canceling hanging call notification [${notification.id}] during service stop")
                        if (notification.tag.isNullOrEmpty()) {
                            androidNotificationManager.cancel(notification.id)
                        } else {
                            androidNotificationManager.cancel(notification.tag, notification.id)
                        }
                    }
                }

                // Try to stop the service using system service manager as backup
                try {
                    Log.i("$TAG Backup: Attempting to stop CoreInCallService using system service manager")
                    val serviceIntent = Intent(context, CoreInCallService::class.java)
                    context.stopService(serviceIntent)
                    Log.i("$TAG Successfully stopped CoreInCallService via system service manager")
                } catch (e: Exception) {
                    Log.w("$TAG Exception while stopping service via system: $e")
                }

                // Final safety check after 1 second to ensure notifications are really gone
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        val remainingNotifications = androidNotificationManager.activeNotifications
                        val remainingCallNotifications = remainingNotifications.filter { notification ->
                            notification.notification?.let { notif ->
                                notif.category == NotificationCompat.CATEGORY_CALL ||
                                notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                            } ?: false
                        }

                        if (remainingCallNotifications.isNotEmpty()) {
                            Log.w("$TAG FINAL SAFETY: Still have ${remainingCallNotifications.size} call notifications after service stop, using nuclear option")
                            for (notification in remainingCallNotifications) {
                                Log.w("$TAG FINAL SAFETY: Canceling persistent notification [${notification.id}] with tag [${notification.tag}]")
                                if (notification.tag.isNullOrEmpty()) {
                                    androidNotificationManager.cancel(notification.id)
                                } else {
                                    androidNotificationManager.cancel(notification.tag, notification.id)
                                }
                            }

                            // If notifications still persist after all this, cancel ALL notifications
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                val finalCheck = androidNotificationManager.activeNotifications.filter { notification ->
                                    notification.notification?.let { notif ->
                                        notif.category == NotificationCompat.CATEGORY_CALL ||
                                        notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                        notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                    } ?: false
                                }

                                if (finalCheck.isNotEmpty()) {
                                    Log.e("$TAG ULTIMATE NUCLEAR OPTION: Call notifications still persist, canceling ALL notifications")
                                    androidNotificationManager.cancelAll()
                                }
                            }, 500) // Additional 500ms for ultimate check
                        } else {
                            Log.i("$TAG Final safety check: No call notifications remaining, cleanup successful")
                        }
                    } catch (e: Exception) {
                        Log.w("$TAG Exception during final safety check: $e")
                    }
                }, 1000) // 1 second delay for final safety check

                Log.i("$TAG Completed aggressive notification cleanup after stopping foreground service")
            } catch (e: Exception) {
                Log.w("$TAG Exception during aggressive notification cleanup: $e")
            }
        }
    }

    @WorkerThread
    private fun getNotifiableForConversation(chatRoom: ChatRoom, messages: Array<ChatMessage>): Notifiable {
        val address = chatRoom.peerAddress.asStringUriOnly()
        // Chat functionality completely removed - return empty notifiable
        var notifiable: Notifiable? = null
        if (notifiable == null) {
            notifiable = Notifiable(LinphoneUtils.getConversationId(chatRoom).hashCode())
            notifiable.myself = LinphoneUtils.getDisplayName(chatRoom.localAddress)
            notifiable.localIdentity = chatRoom.localAddress.asStringUriOnly()
            notifiable.remoteAddress = chatRoom.peerAddress.asStringUriOnly()

            if (chatRoom.hasCapability(ChatRoom.Capabilities.OneToOne.toInt())) {
                notifiable.isGroup = false
            } else {
                notifiable.isGroup = true
                notifiable.groupTitle = chatRoom.subject
            }

            for (message in chatRoom.unreadHistory) {
                if (message.isRead || message.isOutgoing) continue
                val notifiableMessage = getNotifiableForChatMessage(message)
                notifiable.messages.add(notifiableMessage)
            }
        } else {
            for (message in messages) {
                if (message.isRead || message.isOutgoing) continue
                val notifiableMessage = getNotifiableForChatMessage(message)
                notifiable.messages.add(notifiableMessage)
            }
        }

        // Chat functionality completely removed
        return notifiable
    }

    @WorkerThread
    private fun showChatRoomNotification(chatRoom: ChatRoom, messages: Array<ChatMessage>) {
        val notifiable = getNotifiableForConversation(chatRoom, messages)

        if (!chatRoom.hasCapability(ChatRoom.Capabilities.OneToOne.toInt())) {
            if (chatRoom.subject != notifiable.groupTitle) {
                Log.i(
                    "$TAG Updating notification subject from [${notifiable.groupTitle}] to [${chatRoom.subject}]"
                )
                notifiable.groupTitle = chatRoom.subject
            }
        }

        if (notifiable.messages.isNotEmpty()) {
            val me = coreContext.contactsManager.getMePerson(chatRoom.localAddress)
            val pendingIntent = getChatRoomPendingIntent(chatRoom, notifiable.notificationId)
            val notification = createMessageNotification(
                notifiable,
                pendingIntent,
                LinphoneUtils.getConversationId(chatRoom),
                me
            )
            notify(notifiable.notificationId, notification, CHAT_TAG)
        } else {
            Log.w(
                "$TAG No message to display in received aggregated messages"
            )
        }
    }

    @WorkerThread
    private fun showChatMessageReactionNotification(
        chatRoom: ChatRoom,
        reaction: String,
        address: Address,
        message: ChatMessage
    ) {
        val notifiable = getNotifiableForConversation(chatRoom, arrayOf(message))

        // Check if a previous reaction notifiable exists from the same person & for the same message
        val from = address.asStringUriOnly()
        val found = notifiable.messages.find {
            it.isReaction && it.reactionToMessageId == message.messageId && it.reactionFrom == from
        }
        if (found != null) {
            Log.i(
                "$TAG Found a previous notifiable for a reaction from the same person to the same message"
            )
            if (notifiable.messages.remove(found)) {
                Log.i("$TAG Previous reaction notifiable removed")
            } else {
                Log.w("$TAG Failed to remove previous reaction notifiable")
            }
        }

        val contact =
            coreContext.contactsManager.findContactByAddress(address)
        val displayName = contact?.name ?: LinphoneUtils.getDisplayName(address)

        val originalMessage = LinphoneUtils.getPlainTextDescribingMessage(message)
        val text = AppUtils.getString(R.string.notification_chat_message_reaction_received).format(
            reaction,
            originalMessage
        )

        val notifiableMessage = NotifiableMessage(
            text,
            contact,
            displayName,
            address.asStringUriOnly(),
            message.time * 1000, // Linphone timestamps are in seconds
            isOutgoing = false,
            isReaction = true,
            reactionToMessageId = message.messageId,
            reactionFrom = from
        )
        notifiable.messages.add(notifiableMessage)

        if (notifiable.messages.isNotEmpty()) {
            val me = coreContext.contactsManager.getMePerson(chatRoom.localAddress)
            val pendingIntent = getChatRoomPendingIntent(chatRoom, notifiable.notificationId)
            val notification = createMessageNotification(
                notifiable,
                pendingIntent,
                LinphoneUtils.getConversationId(chatRoom),
                me
            )
            notify(notifiable.notificationId, notification, CHAT_TAG)
        } else {
            Log.e(
                "$TAG Notifiable is empty but we should have displayed the reaction!"
            )
        }
    }

    @WorkerThread
    private fun updateConversationNotification(notifiable: Notifiable, remoteAddress: Address) {
        val localAddress = Factory.instance().createAddress(notifiable.localIdentity.orEmpty())
        localAddress ?: return
        val params: ConferenceParams? = null
        val chatRoom: ChatRoom? = coreContext.core.searchChatRoom(
            params,
            localAddress,
            remoteAddress,
            arrayOfNulls<Address>(0)
        )
        chatRoom ?: return

        val me = coreContext.contactsManager.getMePerson(chatRoom.localAddress)
        val pendingIntent = getChatRoomPendingIntent(chatRoom, notifiable.notificationId)
        val notification = createMessageNotification(
            notifiable,
            pendingIntent,
            LinphoneUtils.getConversationId(chatRoom),
            me
        )
        Log.i(
            "$TAG Updating chat notification with ID [${notifiable.notificationId}]"
        )
        notify(notifiable.notificationId, notification, CHAT_TAG)
    }

    @SuppressLint("MissingPermission")
    @WorkerThread
    private fun notify(id: Int, notification: Notification, tag: String? = null) {
        if (Compatibility.isPostNotificationsPermissionGranted(context)) {
            Log.i(
                "$TAG Notifying using ID [$id] and ${if (tag == null) "without tag" else "with tag [$tag]"}"
            )
            try {
                notificationManager.notify(tag, id, notification)
                notificationsMap[id] = notification
            } catch (iae: IllegalArgumentException) {
                if (inCallService == null && tag == null) {
                    // We can't notify using CallStyle if there isn't a foreground service running
                    Log.w(
                        "$TAG Foreground Service hasn't started yet, can't display a CallStyle notification until then: $iae"
                    )
                } else {
                    Log.e("$TAG Illegal Argument Exception occurred: $iae")
                }
            } catch (e: Exception) {
                Log.e("$TAG Exception occurred: $e")
            }
        } else {
            Log.w("$TAG POST_NOTIFICATIONS permission wasn't granted")
        }
    }

    @WorkerThread
    fun cancelNotification(id: Int, tag: String? = null) {
        Log.i(
            "$TAG Canceling notification with ID [$id] and ${if (tag == null) "without tag" else "with tag [$tag]"}"
        )
        notificationManager.cancel(tag, id)
        notificationsMap.remove(id)
    }

    @WorkerThread
    fun cleanupAllCallNotifications() {

        // Remove incoming call notification
        removeIncomingCallNotification()

        // Cancel all call notifications in the map
        val callNotificationsToRemove = callNotificationsMap.toMap()
        Log.i("$TAG CRITICAL: Found ${callNotificationsToRemove.size} call notifications in map to cancel")
        for ((address, notifiable) in callNotificationsToRemove) {
            Log.i("$TAG CRITICAL: Canceling notification [${notifiable.notificationId}] for call [$address]")
            cancelNotification(notifiable.notificationId)

            // Also force cancel using Android NotificationManager as backup
            coreContext.postOnMainThread {
                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                androidNotificationManager.cancel(notifiable.notificationId)
                Log.i("$TAG CRITICAL: Force canceled notification [${notifiable.notificationId}] using Android NotificationManager")
            }
        }
        callNotificationsMap.clear()

        // Stop foreground service if running
        if (inCallServiceForegroundNotificationPublished) {
            stopInCallForegroundService()
        }

        // Reset all notification tracking variables
        currentInCallServiceNotificationId = -1
        inCallServiceForegroundNotificationPublished = false
        waitForInCallServiceForegroundToStopIt = false
        currentlyRingingCallRemoteAddress = null

        // Clear notifications map
        notificationsMap.clear()

        // Force cancel all known call notification IDs
        coreContext.postOnMainThread {
            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            // Cancel common call notification IDs
            androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
            androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

            // Cancel any notification that might be using the current in-call service ID
            if (currentInCallServiceNotificationId != -1) {
                androidNotificationManager.cancel(currentInCallServiceNotificationId)
            }

            // Cancel all active notifications to be extra sure
            try {
                val activeNotifications = androidNotificationManager.activeNotifications
                for (notification in activeNotifications) {
                    // Cancel ALL notifications, not just ones without tags
                    Log.i(
                        "$TAG Found active notification [${notification.id}] with tag [${notification.tag}], cancelling it"
                    )
                    if (notification.tag.isNullOrEmpty()) {
                        androidNotificationManager.cancel(notification.id)
                    } else {
                        androidNotificationManager.cancel(notification.tag, notification.id)
                    }
                }

                // Also try to cancel all possible call notification IDs
                for (id in 1..20) {
                    androidNotificationManager.cancel(id)
                }

                Log.i("$TAG Cancelled all active notifications")
            } catch (e: Exception) {
                Log.w("$TAG Exception while cancelling active notifications: $e")
            }
        }
    }

    @AnyThread
    fun forceCleanupHangingCallNotifications() {
        Log.i("$TAG Force cleanup of hanging call notifications requested")

        coreContext.postOnCoreThread { core ->
            // Only cleanup if there are no active calls
            if (core.callsNb == 0) {
                Log.i("$TAG No active calls, proceeding with force cleanup")
                cleanupAllCallNotifications()

                // Additional aggressive cleanup
                coreContext.postOnMainThread {
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                        try {
                            Log.i("$TAG Scanning all active notifications for call-related ones...")
                            val activeNotifications = androidNotificationManager.activeNotifications
                            Log.i("$TAG Found ${activeNotifications.size} active notifications")

                            for (notification in activeNotifications) {
                                Log.i("$TAG Checking notification [${notification.id}] with tag [${notification.tag}]")

                                // Check if this looks like a call notification
                                val isCallNotification = notification.notification?.let { notif ->
                                    val hasCallCategory = notif.category == NotificationCompat.CATEGORY_CALL
                                    val hasCallChannel = notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                                                        notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                                    val hasCallActions = notif.actions?.any { action ->
                                        val title = action.title?.toString()?.lowercase() ?: ""
                                        title.contains("hang") || title.contains("answer") || title.contains("decline")
                                    } == true

                                    Log.i("$TAG Notification [${notification.id}]: category=$hasCallCategory, channel=$hasCallChannel, actions=$hasCallActions")
                                    hasCallCategory || hasCallChannel || hasCallActions
                                } ?: false

                                if (isCallNotification) {
                                    Log.i("$TAG Force cleanup: removing hanging call notification [${notification.id}] with tag [${notification.tag}]")
                                    if (notification.tag.isNullOrEmpty()) {
                                        androidNotificationManager.cancel(notification.id)
                                    } else {
                                        androidNotificationManager.cancel(notification.tag, notification.id)
                                    }
                                } else {
                                    Log.i("$TAG Notification [${notification.id}] is not a call notification, skipping")
                                }
                            }

                            // Also cancel all known call notification IDs just in case
                            Log.i("$TAG Force canceling all known call notification IDs")
                            androidNotificationManager.cancel(INCOMING_CALL_ID) // ID 1
                            androidNotificationManager.cancel(DUMMY_NOTIF_ID) // ID 3

                            // Cancel the current foreground service notification if any
                            if (currentInCallServiceNotificationId != -1) {
                                Log.i("$TAG Force canceling current foreground service notification [$currentInCallServiceNotificationId]")
                                androidNotificationManager.cancel(currentInCallServiceNotificationId)
                            }

                        } catch (e: Exception) {
                            Log.w("$TAG Exception during force cleanup: $e")
                        }
                    }, 200)
                }
            } else {
                Log.w("$TAG Cannot force cleanup: there are ${core.callsNb} active calls")
            }
        }
    }

    @WorkerThread
    private fun getNotificationIdForCall(call: Call): Int {
        val notificationId = call.callLog.startDate.toInt()
        Log.i("$TAG Generated notification ID [$notificationId] for call [${call.remoteAddress.asStringUriOnly()}]")
        return notificationId
    }

    @AnyThread
    fun logAllActiveNotifications() {
        coreContext.postOnMainThread {
            try {
                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                val activeNotifications = androidNotificationManager.activeNotifications
                Log.i("$TAG === ACTIVE NOTIFICATIONS DEBUG ===")
                Log.i("$TAG Total active notifications: ${activeNotifications.size}")

                for (notification in activeNotifications) {
                    val notif = notification.notification
                    Log.i("$TAG Notification [${notification.id}] tag=[${notification.tag}]:")
                    Log.i("$TAG   - Category: ${notif?.category}")
                    Log.i("$TAG   - Channel: ${notif?.channelId}")
                    Log.i("$TAG   - Actions: ${notif?.actions?.map { it.title }}")
                    Log.i("$TAG   - Ongoing: ${notif?.flags?.and(Notification.FLAG_ONGOING_EVENT) != 0}")
                    Log.i("$TAG   - AutoCancel: ${notif?.flags?.and(Notification.FLAG_AUTO_CANCEL) != 0}")
                }
                Log.i("$TAG === END ACTIVE NOTIFICATIONS DEBUG ===")
            } catch (e: Exception) {
                Log.w("$TAG Exception while logging active notifications: $e")
            }
        }
    }

    @AnyThread
    fun emergencyCleanupAllNotifications() {
        Log.w("$TAG EMERGENCY CLEANUP: Canceling ALL notifications")
        coreContext.postOnMainThread {
            try {
                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                logAllActiveNotifications()
                androidNotificationManager.cancelAll()
                Log.w("$TAG EMERGENCY CLEANUP: All notifications canceled")
            } catch (e: Exception) {
                Log.e("$TAG Exception during emergency cleanup: $e")
            }
        }
    }

    @AnyThread
    fun testNotificationCleanup() {
        Log.i("$TAG TEST: Manual notification cleanup test initiated")
        logAllActiveNotifications()

        coreContext.postOnCoreThread { core ->
            if (core.callsNb == 0) {
                Log.i("$TAG TEST: No active calls, testing cleanup mechanisms")

                // Test 1: Force stop foreground service
                if (inCallServiceForegroundNotificationPublished) {
                    Log.i("$TAG TEST: Stopping foreground service")
                    stopInCallForegroundService()
                }

                // Test 2: Clean up all call notifications
                Log.i("$TAG TEST: Cleaning up all call notifications")
                cleanupAllCallNotifications()

                // Test 3: Force cleanup hanging notifications
                Log.i("$TAG TEST: Force cleanup hanging notifications")
                forceCleanupHangingCallNotifications()

                // Test 4: Log results after 2 seconds
                coreContext.postOnMainThread {
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        Log.i("$TAG TEST: Final results after cleanup:")
                        logAllActiveNotifications()
                    }, 2000)
                }
            } else {
                Log.w("$TAG TEST: Cannot test cleanup, there are ${core.callsNb} active calls")
            }
        }
    }

    @AnyThread
    fun debugNotificationIssue() {
        Log.i("$TAG DEBUG: Starting notification issue debug")
        logAllActiveNotifications()

        coreContext.postOnMainThread {
            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            val activeNotifications = androidNotificationManager.activeNotifications

            Log.i("$TAG DEBUG: === DETAILED NOTIFICATION ANALYSIS ===")
            for (notification in activeNotifications) {
                val notif = notification.notification
                Log.i("$TAG DEBUG: Notification ID [${notification.id}] Tag [${notification.tag}]")
                Log.i("$TAG DEBUG:   - Category: ${notif?.category}")
                Log.i("$TAG DEBUG:   - Channel: ${notif?.channelId}")
                Log.i("$TAG DEBUG:   - Actions: ${notif?.actions?.size ?: 0}")
                notif?.actions?.forEach { action ->
                    Log.i("$TAG DEBUG:     - Action: ${action.title}")
                }

                // Check if this is a call notification
                val isCallNotification = notif?.let { n ->
                    n.category == NotificationCompat.CATEGORY_CALL ||
                    n.channelId == context.getString(R.string.notification_channel_call_id) ||
                    n.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
                } ?: false

                Log.i("$TAG DEBUG:   - Is Call Notification: $isCallNotification")

                if (isCallNotification) {
                    Log.w("$TAG DEBUG: FOUND HANGING CALL NOTIFICATION [${notification.id}] - attempting to cancel")
                    if (notification.tag.isNullOrEmpty()) {
                        androidNotificationManager.cancel(notification.id)
                    } else {
                        androidNotificationManager.cancel(notification.tag, notification.id)
                    }
                }
            }

            // Check our internal state
            Log.i("$TAG DEBUG: === INTERNAL STATE ===")
            Log.i("$TAG DEBUG: currentInCallServiceNotificationId: $currentInCallServiceNotificationId")
            Log.i("$TAG DEBUG: inCallServiceForegroundNotificationPublished: $inCallServiceForegroundNotificationPublished")
            Log.i("$TAG DEBUG: callNotificationsMap size: ${callNotificationsMap.size}")
            Log.i("$TAG DEBUG: notificationsMap size: ${notificationsMap.size}")

            for ((address, notifiable) in callNotificationsMap) {
                Log.i("$TAG DEBUG: callNotificationsMap[$address] = notification ID ${notifiable.notificationId}")
            }

            for ((id, _) in notificationsMap) {
                Log.i("$TAG DEBUG: notificationsMap contains notification ID $id")
            }
        }
    }

    @WorkerThread
    private fun getNotifiableForCall(call: Call): Notifiable {
        val address = call.remoteAddress.asStringUriOnly()
        var notifiable: Notifiable? = callNotificationsMap[address]
        if (notifiable == null) {
            val notificationId = getNotificationIdForCall(call)
            notifiable = Notifiable(notificationId)
            notifiable.remoteAddress = address

            callNotificationsMap[address] = notifiable
            Log.i("$TAG Created new notifiable for call [$address] with notification ID [$notificationId]")
        } else {
            Log.i("$TAG Found existing notifiable for call [$address] with notification ID [${notifiable.notificationId}]")
        }
        return notifiable
    }

    @WorkerThread
    private fun getNotifiableForChatMessage(message: ChatMessage): NotifiableMessage {
        val contact =
            coreContext.contactsManager.findContactByAddress(message.fromAddress)
        val displayName = contact?.name ?: LinphoneUtils.getDisplayName(message.fromAddress)

        val text = LinphoneUtils.getPlainTextDescribingMessage(message)
        val address = message.fromAddress
        val notifiableMessage = NotifiableMessage(
            text,
            contact,
            displayName,
            address.asStringUriOnly(),
            message.time * 1000, // Linphone timestamps are in seconds
            isOutgoing = message.isOutgoing
        )

        for (content in message.contents) {
            if (content.isFile) {
                val path = content.filePath
                if (path != null) {
                    val contentUri = FileUtils.getPublicFilePath(context, path)
                    val filePath = contentUri.toString()
                    val extension = FileUtils.getExtensionFromFileName(filePath)
                    if (extension.isNotEmpty()) {
                        val mime = FileUtils.getMimeTypeFromExtension(extension)
                        notifiableMessage.filePath = contentUri
                        notifiableMessage.fileMime = mime
                        Log.i("$TAG Added file $contentUri with MIME $mime to notification")
                    } else {
                        Log.e("$TAG Couldn't find extension for incoming message with file $path")
                    }
                }
            }
        }

        return notifiableMessage
    }

    @WorkerThread
    private fun createCallNotification(
        call: Call,
        notifiable: Notifiable,
        pendingIntent: PendingIntent?,
        isIncoming: Boolean,
        friend: Friend? = null
    ): Notification {
        val declineIntent = getCallDeclinePendingIntent(notifiable)
        val answerIntent = getCallAnswerPendingIntent(notifiable)

        val remoteAddress = call.callLog.remoteAddress
        val conference = call.conference
        val conferenceInfo = LinphoneUtils.getConferenceInfoIfAny(call)
        val isConference = conference != null || conferenceInfo != null

        val caller = if (isConference) {
            val subject = conferenceInfo?.subject ?: conference?.subject ?: LinphoneUtils.getDisplayName(
                remoteAddress
            )
            Person.Builder()
                .setName(subject)
                .setIcon(
                    AvatarGenerator(context).setInitials(AppUtils.getInitials(subject)).buildIcon()
                )
                .setImportant(false)
                .build()
        } else {
            val contact = friend
                ?: coreContext.contactsManager.findContactByAddress(remoteAddress)
            getPerson(contact, LinphoneUtils.getDisplayName(remoteAddress))
        }

        val isVideo = LinphoneUtils.isVideoEnabled(call)

        val smallIcon = if (isConference) {
            R.drawable.users_three
        } else if (isVideo) {
            R.drawable.phone
        } else {
            R.drawable.phone
        }

        val style = if (isIncoming) {
            if (!Compatibility.hasFullScreenIntentPermission(context)) {
                Log.e(
                    "$TAG Android >= 14 & full screen intent permission wasn't granted, incoming call may not be visible!"
                )
            }
            NotificationCompat.CallStyle.forIncomingCall(
                caller,
                declineIntent,
                answerIntent
            )
        } else {
            NotificationCompat.CallStyle.forOngoingCall(
                caller,
                declineIntent
            )
        }

        val channelId = if (isIncoming) {
            context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
        } else {
            context.getString(R.string.notification_channel_call_id)
        }
        val channel = notificationManager.getNotificationChannel(channelId)
        val importance = channel?.importance ?: NotificationManagerCompat.IMPORTANCE_NONE
        Log.i(
            "Creating notification for ${if (isIncoming) "[incoming] " else ""}[${if (isConference) "conference" else "call"}] with video [${if (isVideo) "enabled" else "disabled"}] on channel [$channel] with importance [$importance]"
        )

        val builder = NotificationCompat.Builder(
            context,
            channelId
        ).apply {
            try {
                style.setIsVideo(isVideo)
                setStyle(style)
            } catch (iae: IllegalArgumentException) {
                Log.e(
                    "$TAG Can't use notification call style: $iae"
                )
            }
            setColor(context.resources.getColor(R.color.gray_600, context.theme))
            setColorized(true)
            setOnlyAlertOnce(true)
            setSmallIcon(smallIcon)
            setCategory(NotificationCompat.CATEGORY_CALL)
            setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            if (isIncoming) {
                setPriority(NotificationCompat.PRIORITY_MAX)
            } else {
                setPriority(NotificationCompat.PRIORITY_HIGH)
            }
            setWhen(call.callLog.startDate * 1000) // Linphone timestamps are in seconds
            setAutoCancel(false)
            setOngoing(true)
            setContentIntent(pendingIntent)
            setFullScreenIntent(pendingIntent, true)
        }

        return builder.build()
    }

    @WorkerThread
    private fun updateCallNotification(
        notifiable: Notifiable,
        remoteAddress: Address,
        friend: Friend
    ) {
        val call = coreContext.core.getCallByRemoteAddress2(remoteAddress)
        if (call == null) {
            Log.w(
                "$TAG Failed to find call with remote SIP URI [${remoteAddress.asStringUriOnly()}]"
            )
            return
        }
        val isIncoming = LinphoneUtils.isCallIncoming(call.state)

        val notification = if (isIncoming) {
            notificationsMap[INCOMING_CALL_ID]
        } else {
            notificationsMap[notifiable.notificationId]
        }
        if (notification == null) {
            Log.w(
                "$TAG Failed to find notification with ID [${notifiable.notificationId}], creating a new one"
            )
            showCallNotification(call, isIncoming, friend)
            return
        }

        val pendingIntent = notification.fullScreenIntent
        val newNotification = createCallNotification(
            call,
            notifiable,
            pendingIntent,
            isIncoming,
            friend
        )
        if (isIncoming) {
            if (!currentlyDisplayedIncomingCallFragment) {
                Log.i("$TAG Updating incoming call notification with ID [$INCOMING_CALL_ID]")
                notify(INCOMING_CALL_ID, newNotification)
            } else {
                Log.i(
                    "$TAG Incoming call fragment is visible, do not re-send an incoming call notification"
                )
            }
        } else {
            Log.i("$TAG Updating call notification with ID [${notifiable.notificationId}]")
            notify(notifiable.notificationId, newNotification)
        }
    }

    @WorkerThread
    private fun createMessageNotification(
        notifiable: Notifiable,
        pendingIntent: PendingIntent,
        id: String,
        me: Person
    ): Notification {
        val style = NotificationCompat.MessagingStyle(me)
        val allPersons = arrayListOf<Person>()

        var lastPersonAvatar: Bitmap? = null
        var lastPerson: Person? = null
        for (message in notifiable.messages) {
            val friend = message.friend
            val person = getPerson(friend, message.sender)

            if (!message.isOutgoing) {
                // We don't want to see our own avatar
                lastPerson = person
                lastPersonAvatar = friend?.getAvatarBitmap()

                if (allPersons.find { it.key == person.key } == null) {
                    allPersons.add(person)
                }
            }

            val senderPerson = if (message.isOutgoing) null else person // Use null for ourselves
            val tmp = NotificationCompat.MessagingStyle.Message(
                message.message,
                message.time,
                senderPerson
            )
            if (message.filePath != null) tmp.setData(message.fileMime, message.filePath)

            style.addMessage(tmp)
            if (message.isOutgoing) {
                style.addHistoricMessage(tmp)
            }
        }

        style.conversationTitle = if (notifiable.isGroup) notifiable.groupTitle else lastPerson?.name
        style.isGroupConversation = notifiable.isGroup
        Log.i(
            "$TAG Conversation is ${if (style.isGroupConversation) "group" else "1-1"} with title [${style.conversationTitle}]"
        )

        val largeIcon = lastPersonAvatar
        val notificationBuilder = NotificationCompat.Builder(
            context,
            context.getString(R.string.notification_channel_chat_id)
        )
            .setSmallIcon(R.drawable.bell_simple)
            .setAutoCancel(true)
            .setLargeIcon(largeIcon)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setGroup(CHAT_NOTIFICATIONS_GROUP)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setNumber(notifiable.messages.size)
            .setWhen(System.currentTimeMillis())
            .setShowWhen(true)
            .setStyle(style)
            .setContentIntent(pendingIntent)
            .addAction(getMarkMessageAsReadAction(notifiable))
            .addAction(getReplyMessageAction(notifiable))
            .setShortcutId(id)
            .setLocusId(LocusIdCompat(id))

        for (person in allPersons) {
            notificationBuilder.addPerson(person)
        }

        // Chat functionality completely removed
        if (false) {
            Log.i("$TAG Conversation will be marked as read upon notification dismissal")
            notificationBuilder
                .setDeleteIntent(getMarkMessageAsReadPendingIntent(notifiable))
        }

        return notificationBuilder.build()
    }

    @WorkerThread
    private fun dismissCallNotification(call: Call) {
        val address = call.remoteAddress.asStringUriOnly()
        val notifiable: Notifiable? = callNotificationsMap[address]
        if (notifiable != null) {
            Log.i("$TAG Dismissing call notification for [$address] with ID [${notifiable.notificationId}]")
            cancelNotification(notifiable.notificationId)
            callNotificationsMap.remove(address)

            // If this was the current in-call service notification, stop the foreground service
            if (notifiable.notificationId == currentInCallServiceNotificationId) {
                Log.i("$TAG This was the in-call service notification, stopping foreground service")
                stopInCallForegroundService()
            }

            // Force cancel the notification using Android's NotificationManager as backup
            coreContext.postOnMainThread {
                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                androidNotificationManager.cancel(notifiable.notificationId)
                Log.i("$TAG Force canceled notification [${notifiable.notificationId}] using Android NotificationManager")
            }
        } else {
            Log.w("$TAG No notification found for call with remote address [$address]")

            // Even if we don't have the notification in our map, it might still exist as a foreground service notification
            // This can happen when calls fail due to errors (incorrect number, network issues, etc.)
            Log.i("$TAG Checking if this call was using the foreground service notification")
            if (inCallServiceForegroundNotificationPublished && currentInCallServiceNotificationId != -1) {
                Log.i("$TAG Call not in map but foreground service is active, force stopping it")
                stopInCallForegroundService()
            }

            // Force cleanup any notification that might be related to this call
            coreContext.postOnMainThread {
                val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

                try {
                    // Try to cancel notifications that might be related to this call
                    val callId = getNotificationIdForCall(call)
                    androidNotificationManager.cancel(callId)
                    Log.i("$TAG Force canceled potential notification [$callId] for call [$address]")

                    // Also scan for any hanging call notifications
                    val activeNotifications = androidNotificationManager.activeNotifications
                    for (notification in activeNotifications) {
                        val isCallNotification = notification.notification?.let { notif ->
                            notif.category == NotificationCompat.CATEGORY_CALL ||
                            notif.channelId == context.getString(R.string.notification_channel_call_id) ||
                            notif.channelId == context.getString(R.string.notification_channel_without_ringtone_incoming_call_id) ||
                            notif.actions?.any { action ->
                                action.title?.toString()?.contains("hang", ignoreCase = true) == true ||
                                action.title?.toString()?.contains("answer", ignoreCase = true) == true ||
                                action.title?.toString()?.contains("decline", ignoreCase = true) == true
                            } == true
                        } ?: false

                        if (isCallNotification) {
                            Log.i("$TAG Found hanging call notification [${notification.id}] during error cleanup, canceling it")
                            if (notification.tag.isNullOrEmpty()) {
                                androidNotificationManager.cancel(notification.id)
                            } else {
                                androidNotificationManager.cancel(notification.tag, notification.id)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.w("$TAG Exception during error case notification cleanup: $e")
                }
            }
        }

        // Additional safety: if this is the last call, ensure incoming call notification is also removed
        if (coreContext.core.callsNb == 0) {
            Log.i("$TAG No more calls, ensuring incoming call notification is removed")
            removeIncomingCallNotification()

            // Also force stop the foreground service if no calls remain
            if (inCallServiceForegroundNotificationPublished) {
                Log.i("$TAG No more calls, force stopping foreground service")
                stopInCallForegroundService()
            }
        }
    }

    @WorkerThread
    fun dismissChatNotification(chatRoom: ChatRoom): Boolean {
        val address = chatRoom.peerAddress.asStringUriOnly()
        // Chat functionality completely removed
        val notifiable: Notifiable? = null
        if (notifiable != null) {
            Log.i(
                "$TAG Dismissing notification for conversation [${chatRoom.peerAddress.asStringUriOnly()}] with id ${notifiable.notificationId}"
            )
            notifiable.messages.clear()
            cancelNotification(notifiable.notificationId, CHAT_TAG)
            return true
        } else {
            val previousNotificationId = previousChatNotifications.find { id ->
                id == LinphoneUtils.getConversationId(chatRoom).hashCode()
            }
            if (previousNotificationId != null) {
                Log.i(
                    "$TAG Found previous notification with same ID [$previousNotificationId], canceling it"
                )
                cancelNotification(previousNotificationId, CHAT_TAG)
                return true
            }
        }
        return false
    }

    @AnyThread
    fun getCallDeclinePendingIntent(notifiable: Notifiable): PendingIntent {
        val hangupIntent = Intent(context, NotificationBroadcastReceiver::class.java)
        hangupIntent.action = INTENT_HANGUP_CALL_NOTIF_ACTION
        hangupIntent.putExtra(INTENT_NOTIF_ID, notifiable.notificationId)
        hangupIntent.putExtra(INTENT_REMOTE_ADDRESS, notifiable.remoteAddress)

        return PendingIntent.getBroadcast(
            context,
            3,
            hangupIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    @AnyThread
    fun getCallAnswerPendingIntent(notifiable: Notifiable): PendingIntent {
        val answerIntent = Intent(context, NotificationBroadcastReceiver::class.java)
        answerIntent.action = INTENT_ANSWER_CALL_NOTIF_ACTION
        answerIntent.putExtra(INTENT_NOTIF_ID, notifiable.notificationId)
        answerIntent.putExtra(INTENT_REMOTE_ADDRESS, notifiable.remoteAddress)

        return PendingIntent.getBroadcast(
            context,
            2,
            answerIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    @WorkerThread
    private fun displayReplyMessageNotification(message: ChatMessage, notifiable: Notifiable) {
        Log.i(
            "$TAG Updating message notification with reply for notification ${notifiable.notificationId}"
        )

        val text = message.contents.find { content -> content.isText }?.utf8Text ?: ""
        val senderAddress = message.fromAddress
        val reply = NotifiableMessage(
            text,
            null,
            notifiable.myself ?: LinphoneUtils.getDisplayName(senderAddress),
            senderAddress.asStringUriOnly(),
            System.currentTimeMillis(),
            isOutgoing = true
        )
        notifiable.messages.add(reply)

        val chatRoom = message.chatRoom
        val pendingIntent = getChatRoomPendingIntent(chatRoom, notifiable.notificationId)
        val me = coreContext.contactsManager.getMePerson(chatRoom.localAddress)
        val notification = createMessageNotification(
            notifiable,
            pendingIntent,
            LinphoneUtils.getConversationId(chatRoom),
            me
        )
        notify(notifiable.notificationId, notification, CHAT_TAG)
    }

    @AnyThread
    private fun getReplyMessageAction(notifiable: Notifiable): NotificationCompat.Action {
        val replyLabel =
            context.resources.getString(R.string.notification_reply_to_message)
        val remoteInput =
            RemoteInput.Builder(KEY_TEXT_REPLY).setLabel(replyLabel).build()

        val replyIntent = Intent(context, NotificationBroadcastReceiver::class.java)
        replyIntent.action = INTENT_REPLY_MESSAGE_NOTIF_ACTION
        replyIntent.putExtra(INTENT_NOTIF_ID, notifiable.notificationId)
        replyIntent.putExtra(INTENT_LOCAL_IDENTITY, notifiable.localIdentity)
        replyIntent.putExtra(INTENT_REMOTE_ADDRESS, notifiable.remoteAddress)

        // PendingIntents attached to actions with remote inputs must be mutable
        val replyPendingIntent = PendingIntent.getBroadcast(
            context,
            notifiable.notificationId,
            replyIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        )
        return NotificationCompat.Action.Builder(
            R.drawable.paper_plane_right,
            context.getString(R.string.notification_reply_to_message),
            replyPendingIntent
        )
            .addRemoteInput(remoteInput)
            .setAllowGeneratedReplies(true)
            .setShowsUserInterface(false)
            .setSemanticAction(NotificationCompat.Action.SEMANTIC_ACTION_REPLY)
            .build()
    }

    @AnyThread
    private fun getMarkMessageAsReadPendingIntent(notifiable: Notifiable): PendingIntent {
        val markAsReadIntent = Intent(context, NotificationBroadcastReceiver::class.java)
        markAsReadIntent.action = INTENT_MARK_MESSAGE_AS_READ_NOTIF_ACTION
        markAsReadIntent.putExtra(INTENT_NOTIF_ID, notifiable.notificationId)
        markAsReadIntent.putExtra(INTENT_LOCAL_IDENTITY, notifiable.localIdentity)
        markAsReadIntent.putExtra(INTENT_REMOTE_ADDRESS, notifiable.remoteAddress)

        return PendingIntent.getBroadcast(
            context,
            notifiable.notificationId,
            markAsReadIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    @AnyThread
    private fun getMarkMessageAsReadAction(notifiable: Notifiable): NotificationCompat.Action {
        val markAsReadPendingIntent = getMarkMessageAsReadPendingIntent(notifiable)
        return NotificationCompat.Action.Builder(
            R.drawable.envelope_simple_open,
            context.getString(R.string.notification_mark_message_as_read),
            markAsReadPendingIntent
        )
            .setShowsUserInterface(false)
            .setSemanticAction(NotificationCompat.Action.SEMANTIC_ACTION_MARK_AS_READ)
            .build()
    }

    @WorkerThread
    private fun getPerson(friend: Friend?, fallbackDisplayName: String): Person {
        return friend?.getPerson()
            ?: Person.Builder()
                .setName(if (fallbackDisplayName.isEmpty()) "Unknown" else fallbackDisplayName)
                .setIcon(
                    AvatarGenerator(context).setInitials(AppUtils.getInitials(fallbackDisplayName)).buildIcon()
                )
                .setKey(fallbackDisplayName)
                .setImportant(false)
                .build()
    }

    @MainThread
    private fun startKeepAliveServiceForeground() {
        Log.i(
            "$TAG Trying to start keep alive for third party accounts foreground Service using call notification"
        )

        val channelId = context.getString(R.string.notification_channel_service_id)
        if (!validateNotificationChannel(channelId)) {
            createChannels(false)
            // Check again after recreation
            if (!validateNotificationChannel(channelId)) {
                return
            }
        }

        val service = keepAliveService
        if (service != null) {
            val pendingIntent = TaskStackBuilder.create(context).run {
                addNextIntentWithParentStack(
                    Intent(context, MainActivity::class.java).apply {
                        action = Intent.ACTION_MAIN // Needed as well
                    }
                )
                getPendingIntent(
                    KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )!!
            }

            val builder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.linphone_notification)
                .setContentText(AppUtils.getString(R.string.notification_keep_app_alive_message))
                .setAutoCancel(false)
                .setOngoing(true)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setShowWhen(false)
                .setContentIntent(pendingIntent)
            val notification = builder.build()

            Log.i(
                "$TAG Keep alive for third party accounts Service found, starting it as foreground using notification ID [$KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID] with type [SPECIAL_USE]"
            )
            try {
                Compatibility.startServiceForeground(
                    service,
                    KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID,
                    notification,
                    Compatibility.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
                )
                currentKeepAliveThirdPartyAccountsForegroundServiceNotificationId = KEEP_ALIVE_FOR_THIRD_PARTY_ACCOUNTS_ID
            } catch (e: Exception) {
                // Try to ensure notification channel exists and is enabled
                coreContext.postOnMainThread {
                    createChannels(false)
                }
            }
        } else {
            Log.w("$TAG Keep alive for third party accounts Service hasn't started yet...")
        }
    }

    @MainThread
    private fun stopKeepAliveServiceForeground() {
        val service = keepAliveService
        if (service != null) {
            Log.i(
                "$TAG Stopping keep alive for third party accounts foreground Service (was using notification ID [$currentKeepAliveThirdPartyAccountsForegroundServiceNotificationId])"
            )
            service.stopForeground(STOP_FOREGROUND_REMOVE)
            service.stopSelf()
        } else {
            Log.w(
                "$TAG Can't stop keep alive for third party accounts foreground Service & notif, no Service was found"
            )
        }
        currentKeepAliveThirdPartyAccountsForegroundServiceNotificationId = -1
    }

    @MainThread
    private fun createIncomingCallNotificationChannelWithoutRingtone() {
        val id = context.getString(R.string.notification_channel_without_ringtone_incoming_call_id)
        val name = context.getString(R.string.notification_channel_incoming_call_name)

        val channel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_HIGH).apply {
            description = name
            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }

    @MainThread
    private fun createMissedCallNotificationChannel() {
        val id = context.getString(R.string.notification_channel_missed_call_id)
        val name = context.getString(R.string.notification_channel_missed_call_name)

        val channel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_HIGH).apply {
            description = name
            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            enableLights(true)
            enableVibration(true)
            setShowBadge(true)
        }
        notificationManager.createNotificationChannel(channel)
    }

    @MainThread
    private fun createActiveCallNotificationChannel() {
        val id = context.getString(R.string.notification_channel_call_id)
        val name = context.getString(R.string.notification_channel_call_name)

        val channel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_LOW).apply {
            description = name
            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            enableLights(false)
            enableVibration(false)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }

    @MainThread
    private fun createMessageChannel() {
        val id = context.getString(R.string.notification_channel_chat_id)
        val name = context.getString(R.string.notification_channel_chat_name)

        val channel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_HIGH).apply {
            description = name
            lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            enableLights(true)
            enableVibration(true)
            setShowBadge(true)
        }
        notificationManager.createNotificationChannel(channel)
    }

    @MainThread
    private fun createThirdPartyAccountKeepAliveServiceChannel() {
        val id = context.getString(R.string.notification_channel_service_id)
        val name = context.getString(R.string.notification_channel_service_name)

        val channel = NotificationChannel(id, name, NotificationManager.IMPORTANCE_LOW).apply {
            description = context.getString(R.string.notification_channel_service_desc)
            enableLights(false)
            enableVibration(false)
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }

    @WorkerThread
    private fun getChatRoomPendingIntent(chatRoom: ChatRoom, notificationId: Int): PendingIntent {
        // Chat functionality disabled - returning empty intent
        val args = Bundle()
        // Chat functionality removed

        // Not using NavDeepLinkBuilder to prevent stacking a ConversationsListFragment above another one
        return TaskStackBuilder.create(context).run {
            addNextIntentWithParentStack(
                Intent(context, MainActivity::class.java).apply {
                    setAction(Intent.ACTION_MAIN) // Needed as well
                    putExtras(args) // Need to pass args here for Chat extra
                }
            )
            getPendingIntent(
                notificationId,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                args // Need to pass args here too for Remote & Local SIP URIs
            )!!
        }
    }

    // Chat message sound playback completely removed

    class Notifiable(val notificationId: Int) {
        var myself: String? = null

        var localIdentity: String? = null
        var remoteAddress: String? = null

        var isGroup: Boolean = false
        var groupTitle: String? = null
        val messages: ArrayList<NotifiableMessage> = arrayListOf()
    }

    class NotifiableMessage(
        val message: String,
        var friend: Friend?,
        var sender: String,
        val senderAddress: String,
        val time: Long,
        var filePath: Uri? = null,
        var fileMime: String? = null,
        val isOutgoing: Boolean = false,
        val isReaction: Boolean = false,
        val reactionToMessageId: String? = null,
        val reactionFrom: String? = null
    )
}
