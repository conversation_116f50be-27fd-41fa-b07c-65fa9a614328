
package org.linphone.utils

import android.content.Context
import androidx.annotation.WorkerThread
import androidx.core.content.pm.ShortcutManagerCompat
import org.linphone.LinphoneApplication.Companion.coreContext
import org.linphone.core.ChatRoom
import org.linphone.core.tools.Log

class ShortcutUtils {
    companion object {
        private const val TAG = "[Shortcut Utils]"

        @WorkerThread
        fun removeShortcutToChatRoom(chatRoom: ChatRoom) {
            val id = LinphoneUtils.getConversationId(chatRoom)
            Log.i("$TAG Removing shortcut to conversation [$id]")
            ShortcutManagerCompat.removeLongLivedShortcuts(coreContext.context, arrayListOf(id))
        }

        @WorkerThread
        fun createShortcutsToChatRooms(context: Context) {
            // Chat functionality removed - no shortcuts created
            Log.i("$TAG Chat functionality removed, no shortcuts to create")
        }

        @WorkerThread
        fun isShortcutToChatRoomAlreadyCreated(context: Context, chatRoom: ChatRoom): Boolean {
            // Chat functionality removed
            return false
        }
    }
}
