package org.linphone.ui.splash

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.phantom.caller.R
import org.linphone.core.tools.Log
import org.linphone.ui.main.MainActivity

class SplashActivity : AppCompatActivity() {

    private val permissionsToRequest = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.READ_CONTACTS,
        Manifest.permission.POST_NOTIFICATIONS
    )

    private val requestPermissionsLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        var allGranted = true
        permissions.entries.forEach { (permission, isGranted) ->
            if (!isGranted) {
                allGranted = false
                Log.w("[Splash Activity] Permission $permission denied")
            }
        }

        if (allGranted) {
            navigateToMainApp()
        } else {
            // Handle the case where some permissions are denied
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        requestPermissions()
    }

    private fun requestPermissions() {
        val permissionsToRequestNow = permissionsToRequest.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }.toTypedArray()

        if (permissionsToRequestNow.isEmpty()) {
            navigateToMainApp()
        } else {
            requestPermissionsLauncher.launch(permissionsToRequestNow)
        }
    }

    private fun navigateToMainApp() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
