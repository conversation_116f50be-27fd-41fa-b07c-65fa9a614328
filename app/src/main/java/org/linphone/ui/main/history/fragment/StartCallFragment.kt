package org.linphone.ui.main.history.fragment

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.UiThread
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.phantom.caller.R
import org.linphone.core.tools.Log
import com.phantom.caller.databinding.StartCallFragmentBinding
import com.phantom.caller.databinding.StartCallNumpadBottomSheetBinding
import org.linphone.ui.main.fragment.GenericMainFragment
import org.linphone.ui.main.history.viewmodel.StartCallViewModel
import org.linphone.utils.hideKeyboard

class StartCallFragment : GenericMainFragment() {
    companion object {
        private const val TAG = "[Start Call Fragment]"
    }

    private lateinit var binding: StartCallFragmentBinding

    private val viewModel: StartCallViewModel by navGraphViewModels(R.id.main_nav_graph)

    private var numpadBottomSheetDialog: Dialog? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = StartCallFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel

        binding.backClickListener = View.OnClickListener {
            goBack()
        }

        binding.hideNumpadClickListener = View.OnClickListener {
            hideNumpad()
        }

        binding.askForGroupCallSubjectClickListener = View.OnClickListener {
            // Handle group call subject request
            Log.i("$TAG Group call subject requested")
        }

        viewModel.isNumpadVisible.observe(viewLifecycleOwner) { visible ->
            if (visible == true) {
                showNumpad()
            } else {
                hideNumpad()
            }
        }

        // Observe events from numpad to update search field
        viewModel.appendDigitToSearchBarEvent.observe(viewLifecycleOwner) { event ->
            event.consume { digit ->
                Log.i("$TAG Appending digit [$digit] to search bar")
                val currentText = viewModel.searchFilter.value.orEmpty()
                viewModel.searchFilter.value = currentText + digit
            }
        }

        viewModel.removedCharacterAtCurrentPositionEvent.observe(viewLifecycleOwner) { event ->
            event.consume {
                Log.i("$TAG Removing character from search bar")
                val currentText = viewModel.searchFilter.value.orEmpty()
                if (currentText.isNotEmpty()) {
                    viewModel.searchFilter.value = currentText.dropLast(1)
                }
            }
        }

        viewModel.clearSearchBarEvent.observe(viewLifecycleOwner) { event ->
            event.consume {
                Log.i("$TAG Clearing search bar")
                viewModel.searchFilter.value = ""
            }
        }

        viewModel.leaveFragmentEvent.observe(viewLifecycleOwner) { event ->
            event.consume {
                Log.i("$TAG Call initiated, leaving fragment")
                goBack()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // Initialize search filter if not set
        if (viewModel.searchFilter.value == null) {
            viewModel.searchFilter.value = ""
        }
        
        // Automatically show numpad when fragment opens
        Log.i("$TAG Automatically showing numpad on fragment resume")
        viewModel.isNumpadVisible.value = true
    }

    override fun onPause() {
        super.onPause()
        binding.root.hideKeyboard()
    }

    override fun onDestroy() {
        numpadBottomSheetDialog?.dismiss()
        super.onDestroy()
    }

    override fun goBack(): Boolean {
        if (findNavController().currentDestination?.id == R.id.startCallFragment) {
            findNavController().popBackStack()
            return true
        }
        return false
    }

    @UiThread
    private fun showNumpad() {
        if (numpadBottomSheetDialog != null) {
            Log.w("$TAG Numpad bottom sheet dialog already created!")
            return
        }

        val numpadBinding = StartCallNumpadBottomSheetBinding.inflate(layoutInflater)
        numpadBinding.lifecycleOwner = viewLifecycleOwner
        numpadBinding.model = viewModel.numpadModel

        val dialog = BottomSheetDialog(requireContext())
        dialog.setContentView(numpadBinding.root)

        val behavior = BottomSheetBehavior.from(numpadBinding.root.parent as View)
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.skipCollapsed = true

        numpadBottomSheetDialog = dialog
        dialog.show()

        dialog.setOnDismissListener {
            Log.i("$TAG Numpad bottom sheet dialog dismissed")
            numpadBottomSheetDialog = null
            viewModel.isNumpadVisible.value = false
        }
    }

    @UiThread
    private fun hideNumpad() {
        numpadBottomSheetDialog?.dismiss()
    }
}
