
package org.linphone.ui.main.viewmodel

import androidx.annotation.UiThread
import androidx.lifecycle.MutableLiveData
import org.linphone.LinphoneApplication.Companion.coreContext
import com.phantom.caller.R
import org.linphone.core.Address
import org.linphone.core.tools.Log
import org.linphone.ui.main.model.SelectedAddressModel
import org.linphone.utils.AppUtils
import org.linphone.utils.Event

class AddParticipantsViewModel
    @UiThread
    constructor() : DefaultAccountChangedViewModel() {
    companion object {
        private const val TAG = "[Add Participants ViewModel]"
    }

    val selectedSipUrisEvent = MutableLiveData<Event<ArrayList<String>>>()
    
    val selection = MutableLiveData<ArrayList<SelectedAddressModel>>()
    val selectionCount = MutableLiveData<String>()
    val searchFilter = MutableLiveData<String>()

    val isEmpty = MutableLiveData<Boolean>()
    val searchInProgress = MutableLiveData<Boolean>()

    @UiThread
    fun clearFilter() {
        searchFilter.value = ""
    }

    private fun removeAddressModelFromSelection(model: SelectedAddressModel) {
        // Simple implementation for removing from selection
        val currentList = selection.value.orEmpty().toMutableList()
        currentList.remove(model)
        selection.postValue(ArrayList(currentList))
    }

    @UiThread
    fun isSelectionEmpty(): Boolean {
        return selection.value.orEmpty().isEmpty()
    }

    @UiThread
    fun addSelectedParticipants(participants: Array<String>) {
        coreContext.postOnCoreThread { core ->
            Log.i("$TAG Adding [${participants.size}] pre-selected participants")
            val list = arrayListOf<SelectedAddressModel>()
            val addresses = arrayListOf<Address>()

            for (uri in participants) {
                val address = core.interpretUrl(uri, false)
                if (address == null) {
                    Log.e("$TAG Failed to parse participant URI [$uri] as address!")
                    continue
                }
                addresses.add(address)

                val avatarModel = coreContext.contactsManager.getContactAvatarModelForAddress(
                    address
                )
                val model = SelectedAddressModel(address, avatarModel) {
                    removeAddressModelFromSelection(it)
                }
                list.add(model)
            }

            selectionCount.postValue(
                AppUtils.getStringWithPlural(
                    R.plurals.selection_count_label,
                    list.size,
                    list.size.toString()
                )
            )
            selection.postValue(list)
            // updateSelectedParticipants(addresses) // Method removed with chat functionality
        }
    }

    @UiThread
    fun addParticipants() {
        val selected = selection.value.orEmpty()
        Log.i("$TAG [${selected.size}] participants selected")

        coreContext.postOnCoreThread {
            val list = arrayListOf<String>()
            for (model in selected) {
                list.add(model.address.asStringUriOnly())
            }

            selectedSipUrisEvent.postValue(Event(list))
        }
    }
}
