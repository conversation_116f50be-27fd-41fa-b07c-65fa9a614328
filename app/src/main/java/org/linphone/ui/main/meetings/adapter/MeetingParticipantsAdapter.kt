
package org.linphone.ui.main.meetings.adapter
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.UiThread
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.phantom.caller.R
import com.phantom.caller.databinding.MeetingParticipantListCellBinding
import org.linphone.ui.main.meetings.model.ParticipantModel

class MeetingParticipantsAdapter : ListAdapter<ParticipantModel, RecyclerView.ViewHolder>(
    MeetingParticipantDiffCallback()
) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val binding: MeetingParticipantListCellBinding = DataBindingUtil.inflate(
            LayoutInflater.from(parent.context),
            R.layout.meeting_participant_list_cell,
            parent,
            false
        )
        binding.lifecycleOwner = parent.findViewTreeLifecycleOwner()
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as ViewHolder).bind(getItem(position))
    }

    inner class ViewHolder(
        val binding: MeetingParticipantListCellBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        @UiThread
        fun bind(participantModel: ParticipantModel) {
            with(binding) {
                model = participantModel
                executePendingBindings()
            }
        }
    }

    private class MeetingParticipantDiffCallback : DiffUtil.ItemCallback<ParticipantModel>() {
        override fun areItemsTheSame(oldItem: ParticipantModel, newItem: ParticipantModel): Boolean {
            return oldItem.sipUri == newItem.sipUri
        }

        override fun areContentsTheSame(oldItem: ParticipantModel, newItem: ParticipantModel): Boolean {
            return oldItem.avatarModel.id == newItem.avatarModel.id
        }
    }
}
