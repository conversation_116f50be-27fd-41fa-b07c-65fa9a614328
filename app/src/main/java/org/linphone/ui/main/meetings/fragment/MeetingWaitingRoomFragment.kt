
package org.linphone.ui.main.meetings.fragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.phantom.caller.R
import org.linphone.core.tools.Log
import com.phantom.caller.databinding.MeetingWaitingRoomFragmentBinding
import org.linphone.ui.GenericActivity
import org.linphone.ui.call.fragment.AudioDevicesMenuDialogFragment
import org.linphone.ui.call.model.AudioDeviceModel
import org.linphone.ui.main.fragment.GenericMainFragment
import org.linphone.ui.main.meetings.viewmodel.MeetingWaitingRoomViewModel

@UiThread
class MeetingWaitingRoomFragment : GenericMainFragment() {
    companion object {
        private const val TAG = "[Meeting Waiting Room Fragment]"
    }

    private lateinit var binding: MeetingWaitingRoomFragmentBinding

    private lateinit var viewModel: MeetingWaitingRoomViewModel

    private val args: MeetingWaitingRoomFragmentArgs by navArgs()

    // Camera permission launcher removed - audio-only app

    private var bottomSheetDialog: BottomSheetDialogFragment? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = MeetingWaitingRoomFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun goBack(): Boolean {
        try {
            return findNavController().popBackStack()
        } catch (ise: IllegalStateException) {
            Log.e("$TAG Can't go back popping back stack: $ise")
        }
        return false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        postponeEnterTransition()
        super.onViewCreated(view, savedInstanceState)

        binding.lifecycleOwner = viewLifecycleOwner

        viewModel = ViewModelProvider(this)[MeetingWaitingRoomViewModel::class.java]
        binding.viewModel = viewModel
        observeToastEvents(viewModel)

        val uri = args.conferenceUri
        Log.i(
            "$TAG Looking up for conference with SIP URI [$uri]"
        )
        viewModel.findConferenceInfo(uri)

        binding.setBackClickListener {
            goBack()
        }

        viewModel.showAudioDevicesListEvent.observe(viewLifecycleOwner) {
            it.consume { devices ->
                showAudioRoutesMenu(devices)
            }
        }

        viewModel.conferenceInfoFoundEvent.observe(viewLifecycleOwner) {
            it.consume { found ->
                if (found) {
                    startPostponedEnterTransition()
                } else {
                    Log.e("$TAG Failed to find meeting with URI [$uri], going back")
                    goBack()
                }
            }
        }

        viewModel.leaveWaitingRoomEvent.observe(viewLifecycleOwner) {
            it.consume {
                Log.i("$TAG Leaving waiting room")
                goBack()
            }
        }

        viewModel.conferenceCreationError.observe(viewLifecycleOwner) {
            it.consume {
                Log.e("$TAG Error joining the conference!")
                val message = getString(
                    R.string.meeting_waiting_room_failed_to_join_toast
                )
                val icon = R.drawable.warning_circle
                (requireActivity() as GenericActivity).showRedToast(message, icon)
            }
        }

        // Camera permission handling removed - audio-only app
    }

    override fun onResume() {
        super.onResume()

        // Video preview setup removed - audio-only app
    }

    override fun onPause() {
        bottomSheetDialog?.dismiss()
        bottomSheetDialog = null

        // Video preview cleanup removed - audio-only app

        super.onPause()
    }

    // Camera permission check removed - audio-only app

    // enableVideoPreview method removed - audio-only app

    private fun showAudioRoutesMenu(devicesList: List<AudioDeviceModel>) {
        val modalBottomSheet = AudioDevicesMenuDialogFragment(devicesList)
        modalBottomSheet.show(parentFragmentManager, AudioDevicesMenuDialogFragment.TAG)
        bottomSheetDialog = modalBottomSheet
    }
}
