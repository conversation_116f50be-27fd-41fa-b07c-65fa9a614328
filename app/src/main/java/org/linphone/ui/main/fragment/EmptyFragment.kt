
package org.linphone.ui.main.fragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.UiThread
import androidx.navigation.fragment.findNavController
import com.phantom.caller.databinding.EmptyFragmentBinding
import org.linphone.ui.GenericFragment

@UiThread
class EmptyFragment : GenericFragment() {
    private lateinit var binding: EmptyFragmentBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = EmptyFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.lifecycleOwner = viewLifecycleOwner
    }

    override fun onResume() {
        super.onResume()

        findNavController().popBackStack()
    }
}
