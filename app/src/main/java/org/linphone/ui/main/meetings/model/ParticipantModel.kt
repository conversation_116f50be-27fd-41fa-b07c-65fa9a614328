
package org.linphone.ui.main.meetings.model

import androidx.annotation.WorkerThread
import org.linphone.LinphoneApplication.Companion.coreContext
import org.linphone.core.Address

class ParticipantModel
    @WorkerThread
    constructor(address: Address, val isOrganizer: <PERSON><PERSON><PERSON>) {
    val sipUri = address.asStringUriOnly()

    val avatarModel = coreContext.contactsManager.getContactAvatarModelForAddress(address)
}
