
package org.linphone.ui.main.fragment

import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import org.linphone.core.tools.Log
import org.linphone.ui.main.viewmodel.DefaultAccountChangedViewModel

@UiThread
abstract class SlidingPaneChildFragment : GenericMainFragment() {
    companion object {
        private const val TAG = "[Sliding Pane Child Fragment]"
    }

    private lateinit var defaultAccountChangedViewModel: DefaultAccountChangedViewModel

    private val onBackPressedCallback = object : OnBackPressedCallback(false) {
        override fun handleOnBackPressed() {
            Log.d("$TAG ${getFragmentRealClassName()} handleOnBackPressed")
            try {
                if (!goBack()) {
                    Log.d(
                        "$TAG ${getFragmentRealClassName()}'s goBack() method returned false, disabling back pressed callback and trying again"
                    )
                    isEnabled = false
                    try {
                        requireActivity().onBackPressedDispatcher.onBackPressed()
                    } catch (ise: IllegalStateException) {
                        Log.w(
                            "$TAG ${getFragmentRealClassName()} Can't go back: $ise"
                        )
                    }
                }
            } catch (ise: IllegalStateException) {
                Log.e(
                    "$TAG ${getFragmentRealClassName()} Can't go back: $ise"
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            onBackPressedCallback
        )

        defaultAccountChangedViewModel = ViewModelProvider(this)[DefaultAccountChangedViewModel::class.java]
        defaultAccountChangedViewModel.defaultAccountChangedEvent.observe(viewLifecycleOwner) {
            it.consume {
                Log.i("$TAG Default account changed, leaving fragment")
                goBack()
            }
        }

        sharedViewModel.isSlidingPaneSlideable.observe(viewLifecycleOwner) { slideable ->
            val enabled = backPressedCallBackEnabled(slideable)
            onBackPressedCallback.isEnabled = enabled
            Log.d(
                "$TAG ${getFragmentRealClassName()} Our own back press callback is ${if (enabled) "enabled" else "disabled"}"
            )
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        onBackPressedCallback.remove()
    }

    override fun goBack(): Boolean {
        if (!findNavController().popBackStack()) {
            Log.d("$TAG ${getFragmentRealClassName()} Couldn't pop back stack")
            if (!findNavController().navigateUp()) {
                Log.d("$TAG ${getFragmentRealClassName()} Couldn't navigate up")
                onBackPressedCallback.isEnabled = false
                return super.goBack()
            }
            return false
        }
        return false
    }

    private fun backPressedCallBackEnabled(slideable: Boolean): Boolean {
        // This allow to navigate a SlidingPane child nav graph.
        // This only concerns fragments for which the nav graph is inside a SlidingPane layout.
        // In our case it's all graphs except the main one.
        Log.d(
            "$TAG ${getFragmentRealClassName()} Sliding pane is ${if (slideable) "slideable" else "flat"}"
        )
        return slideable
    }
}
