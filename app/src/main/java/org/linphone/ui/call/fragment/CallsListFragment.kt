
package org.linphone.ui.call.fragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.doOnLayout
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import org.linphone.core.tools.Log
import com.phantom.caller.databinding.CallsListFragmentBinding
import org.linphone.ui.call.adapter.CallsListAdapter
import org.linphone.ui.call.viewmodel.CallsViewModel
import org.linphone.ui.call.viewmodel.CurrentCallViewModel
import org.linphone.utils.ConfirmationDialogModel
import org.linphone.utils.DialogUtils

class CallsListFragment : GenericCallFragment() {
    companion object {
        private const val TAG = "[Calls List Fragment]"
    }

    private lateinit var binding: CallsListFragmentBinding

    private lateinit var viewModel: CallsViewModel

    private lateinit var callViewModel: CurrentCallViewModel

    private lateinit var adapter: CallsListAdapter

    private var bottomSheetDialog: BottomSheetDialogFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        adapter = CallsListAdapter()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = CallsListFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel = requireActivity().run {
            ViewModelProvider(this)[CallsViewModel::class.java]
        }

        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        observeToastEvents(viewModel)

        callViewModel = requireActivity().run {
            ViewModelProvider(this)[CurrentCallViewModel::class.java]
        }
        observeToastEvents(callViewModel)

        binding.callsList.setHasFixedSize(true)
        binding.callsList.layoutManager = LinearLayoutManager(requireContext())

        adapter.callLongClickedEvent.observe(viewLifecycleOwner) {
            it.consume { model ->
                val modalBottomSheet = CallMenuDialogFragment(model) {
                    // onDismiss
                    adapter.resetSelection()
                }
                modalBottomSheet.show(parentFragmentManager, CallMenuDialogFragment.TAG)
                bottomSheetDialog = modalBottomSheet
            }
        }

        adapter.callClickedEvent.observe(viewLifecycleOwner) {
            it.consume { model ->
                model.togglePauseResume()
            }
        }

        binding.setBackClickListener {
            findNavController().popBackStack()
        }

        binding.setMergeCallsClickListener {
            showMergeCallsIntoConferenceConfirmationDialog()
        }

        // Video functionality removed - audio-only app

        viewModel.calls.observe(viewLifecycleOwner) {
            Log.i("$TAG Calls list updated with [${it.size}] items")
            adapter.submitList(it)

            // Wait for adapter to have items before setting it in the RecyclerView,
            // otherwise scroll position isn't retained
            if (binding.callsList.adapter != adapter) {
                binding.callsList.adapter = adapter
            }
        }
    }

    override fun onResume() {
        super.onResume()

        (binding.root as? ViewGroup)?.doOnLayout {
            // Video preview removed - audio-only app
        }
    }

    override fun onPause() {
        super.onPause()

        bottomSheetDialog?.dismiss()
        bottomSheetDialog = null

        // Video preview removed - audio-only app
    }

    private fun showMergeCallsIntoConferenceConfirmationDialog() {
        val model = ConfirmationDialogModel()
        val dialog = DialogUtils.getConfirmMergeCallsDialog(
            requireActivity(),
            model
        )

        model.dismissEvent.observe(viewLifecycleOwner) {
            it.consume {
                dialog.dismiss()
            }
        }

        model.confirmEvent.observe(viewLifecycleOwner) {
            it.consume {
                viewModel.mergeCallsIntoConference()
                dialog.dismiss()
            }
        }

        dialog.show()
    }
}
