
package org.linphone.ui.call.viewmodel

import androidx.annotation.UiThread
import androidx.lifecycle.MutableLiveData
import androidx.window.layout.FoldingFeature
import org.linphone.ui.GenericViewModel
import org.linphone.utils.Event

class SharedCallViewModel
    @UiThread
    constructor() : GenericViewModel() {
    val toggleFullScreenEvent = MutableLiveData<Event<Boolean>>()

    val foldingState = MutableLiveData<FoldingFeature>()

    // For moving video preview purposes
    var videoPreviewX: Float = 0f
    var videoPreviewY: Float = 0f
}
