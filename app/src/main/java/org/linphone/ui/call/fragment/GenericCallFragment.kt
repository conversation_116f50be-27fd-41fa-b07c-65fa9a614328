
package org.linphone.ui.call.fragment

import android.os.Bundle
import android.view.View
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModelProvider
import org.linphone.ui.GenericFragment
// RoundCornersTextureView removed - audio-only app
import org.linphone.ui.call.viewmodel.SharedCallViewModel

@UiThread
abstract class GenericCallFragment : GenericFragment() {
    companion object {
        private const val TAG = "[Generic Call Fragment]"
    }

    protected lateinit var sharedViewModel: SharedCallViewModel

    // Video preview functionality removed - audio-only app

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sharedViewModel = requireActivity().run {
            ViewModelProvider(this)[SharedCallViewModel::class.java]
        }
    }

    // Video preview methods removed - audio-only app
}
