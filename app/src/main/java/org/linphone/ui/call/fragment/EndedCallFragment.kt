
package org.linphone.ui.call.fragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.UiThread
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.linphone.core.tools.Log
import com.phantom.caller.databinding.CallEndedFragmentBinding
import org.linphone.ui.call.viewmodel.CurrentCallViewModel

@UiThread
class EndedCallFragment : GenericCallFragment() {
    companion object {
        private const val TAG = "[Ended Call Fragment]"

        private const val LOCALLY_TERMINATED_CALL_TIMEOUT: Long = 1000
        private const val REMOTELY_TERMINATED_CALL_TIMEOUT: Long = 2000
    }

    private lateinit var binding: CallEndedFragmentBinding

    private lateinit var callViewModel: CurrentCallViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = CallEndedFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Disable back gesture / button
        requireActivity().onBackPressedDispatcher.addCallback { }

        callViewModel = requireActivity().run {
            ViewModelProvider(this)[CurrentCallViewModel::class.java]
        }
        observeToastEvents(callViewModel)

        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = callViewModel

        Log.i("$TAG Showing ended call fragment")
    }

    override fun onResume() {
        super.onResume()

        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                if (callViewModel.terminatedByUser) {
                    Log.i(
                        "$TAG Call terminated by user, waiting 1 second before finishing activity"
                    )
                    delay(LOCALLY_TERMINATED_CALL_TIMEOUT)
                } else {
                    Log.i(
                        "$TAG Call terminated by remote end, waiting 2 seconds before finishing activity"
                    )
                    delay(REMOTELY_TERMINATED_CALL_TIMEOUT)
                }

                withContext(Dispatchers.Main) {
                    Log.i("$TAG Finishing activity")
                    requireActivity().finish()
                }
            }
        }
    }
}
