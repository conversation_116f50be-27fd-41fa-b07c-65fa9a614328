
package org.linphone.compatibility

import android.app.Activity
import android.app.Notification
import android.app.PictureInPictureParams
import android.app.Service
import android.net.Uri
import android.provider.MediaStore
import org.linphone.core.tools.Log
import org.linphone.utils.AppUtils

class Api28Compatibility {
    companion object {
        private const val TAG = "[API 28 Compatibility]"

        fun startServiceForeground(service: Service, id: Int, notification: Notification) {
            try {
                service.startForeground(
                    id,
                    notification
                )
            } catch (e: Exception) {
                Log.e("$TAG Can't start service as foreground! $e")
                throw e
            }
        }

        fun enterPipMode(activity: Activity): Boolean {
            val params = PictureInPictureParams.Builder()
                .setAspectRatio(AppUtils.getPipRatio(activity))
                .build()
            try {
                if (!activity.enterPictureInPictureMode(params)) {
                    Log.e("$TAG Failed to enter PiP mode")
                } else {
                    Log.i("$TAG Entered PiP mode")
                    return true
                }
            } catch (e: Exception) {
                Log.e("$TAG Can't build PiP params: $e")
            }
            return false
        }

        fun getMediaCollectionUri(isImage: Boolean, isVideo: Boolean, isAudio: Boolean): Uri {
            return when {
                isImage -> {
                    MediaStore.Images.Media.getContentUri("external")
                }
                isVideo -> {
                    MediaStore.Video.Media.getContentUri("external")
                }
                isAudio -> {
                    MediaStore.Audio.Media.getContentUri("external")
                }
                else -> Uri.EMPTY
            }
        }
    }
}
