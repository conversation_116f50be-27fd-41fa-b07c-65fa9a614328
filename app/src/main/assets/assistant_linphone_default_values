<?xml version="1.0" encoding="UTF-8"?>
<config xmlns="http://www.linphone.org/xsds/lpconfig.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.linphone.org/xsds/lpconfig.xsd lpconfig.xsd">
  <section name="proxy_default_values">
    <entry name="avpf" overwrite="true">1</entry>
    <entry name="dial_escape_plus" overwrite="true">0</entry>
    <entry name="publish" overwrite="true">1</entry>
    <entry name="publish_expires" overwrite="true">120</entry>
    <entry name="quality_reporting_collector" overwrite="true">sip:<EMAIL>;transport=tls</entry>
    <entry name="quality_reporting_enabled" overwrite="true">1</entry>
    <entry name="quality_reporting_interval" overwrite="true">180</entry>
    <entry name="reg_expires" overwrite="true">31536000</entry>
    <entry name="reg_identity" overwrite="true">sip:?@sip.linphone.org</entry>
    <entry name="reg_proxy" overwrite="true">&lt;sip:sip.linphone.org;transport=tls&gt;</entry>
    <entry name="reg_route" overwrite="true">&lt;sip:sip.linphone.org;transport=tls&gt;</entry>
    <entry name="reg_sendregister" overwrite="true">1</entry>
    <entry name="nat_policy_ref" overwrite="true">nat_policy_default_values</entry>
    <entry name="realm" overwrite="true">sip.linphone.org</entry>
    <entry name="conference_factory_uri" overwrite="true">sip:<EMAIL></entry>
    <!-- Video conference factory URI removed - audio-only app -->
    <!-- <entry name="audio_video_conference_factory_uri" overwrite="true">sip:<EMAIL></entry> -->
    <entry name="push_notification_allowed" overwrite="true">1</entry>
    <entry name="cpim_in_basic_chat_rooms_enabled" overwrite="true">1</entry>
    <entry name="rtp_bundle" overwrite="true">1</entry>
    <entry name="lime_server_url" overwrite="true">https://lime.linphone.org/lime-server/lime-server.php</entry>
    <entry name="lime_algo" overwrite="true">c25519</entry>
    <entry name="supported" overwrite="true"></entry>
  </section>
  <section name="nat_policy_default_values">
    <entry name="stun_server" overwrite="true">stun.linphone.org</entry>
    <entry name="protocols" overwrite="true">stun,ice</entry>
  </section>
  <section name="sip">
    <entry name="media_encryption" overwrite="true">srtp</entry>
    <entry name="media_encryption_mandatory">1</entry>
  </section>
</config>
