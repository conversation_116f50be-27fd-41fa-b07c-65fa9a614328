<?xml version="1.0" encoding="UTF-8"?>
<config xmlns="http://www.linphone.org/xsds/lpconfig.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.linphone.org/xsds/lpconfig.xsd lpconfig.xsd">
  <section name="proxy_default_values">
    <entry name="avpf" overwrite="true">0</entry>
    <entry name="dial_escape_plus" overwrite="true">0</entry>
    <entry name="publish" overwrite="true">0</entry>
    <entry name="publish_expires" overwrite="true">-1</entry>
    <entry name="quality_reporting_collector" overwrite="true"></entry>
    <entry name="quality_reporting_enabled" overwrite="true">0</entry>
    <entry name="quality_reporting_interval" overwrite="true">0</entry>
    <entry name="reg_expires" overwrite="true">3600</entry>
    <entry name="reg_identity" overwrite="true"></entry>
    <entry name="reg_proxy" overwrite="true"></entry>
    <entry name="reg_route" overwrite="true"></entry>
    <entry name="reg_sendregister" overwrite="true">1</entry>
    <entry name="nat_policy_ref" overwrite="true"></entry>
    <entry name="realm" overwrite="true"></entry>
    <entry name="conference_factory_uri" overwrite="true"></entry>
    <!-- Video conference factory URI removed - audio-only app -->
    <!-- <entry name="audio_video_conference_factory_uri" overwrite="true"></entry> -->
    <entry name="push_notification_allowed" overwrite="true">0</entry>
    <entry name="cpim_in_basic_chat_rooms_enabled" overwrite="true">0</entry>
    <entry name="rtp_bundle" overwrite="true">0</entry>
    <entry name="lime_server_url" overwrite="true"></entry>
    <entry name="lime_algo" overwrite="true"></entry>
    <entry name="supported" overwrite="true">outbound</entry>
  </section>
  <section name="nat_policy_default_values">
    <entry name="stun_server" overwrite="true">stun.linphone.org</entry>
    <entry name="protocols" overwrite="true">stun,ice</entry>
  </section>
  <section name="sip">
    <entry name="media_encryption">srtp</entry>
    <entry name="media_encryption_mandatory" overwrite="true">0</entry>
  </section>
</config>
