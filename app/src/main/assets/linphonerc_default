
## Start of default rc

[sip]
contact="Linphone Android" <sip:linphone.android@unknown-host>
use_info=0
use_ipv6=1
keepalive_period=30000
sip_port=-1
sip_tcp_port=-1
sip_tls_port=-1
media_encryption=none
update_presence_model_timestamp_before_publish_expires_refresh=1
use_rfc2833=1
use_info=1
rls_uri=sips:<EMAIL>

[net]
#Because dynamic bitrate adaption can increase bitrate, we must allow "no limit"
download_bw=0
upload_bw=0

[video]
enabled=0
size=vga
automatically_accept=0
automatically_initiate=0
automatically_accept_direction=0 #disabled

[app]
tunnel=disabled
auto_download_incoming_voice_recordings=1
auto_download_incoming_icalendars=1

[tunnel]
host=
port=443

[misc]
log_collection_upload_server_url=https://files.linphone.org/http-file-transfer-server/hft.php
file_transfer_server_url=https://files.linphone.org/http-file-transfer-server/hft.php
version_check_url_root=https://download.linphone.org/releases
max_calls=10
history_max_size=100
conference_layout=1
hide_empty_chat_rooms=1

[fec]
fec_enabled=1

[magic_search]
return_empty_friends=1

[chat]
imdn_to_everybody_threshold=1

[ui]
contacts_filter=sip.linphone.org

## End of default rc
